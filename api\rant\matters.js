import { post, get, put, del } from '@/utils/request.uni';
// 查询督办事项列表
export function listMatters(query) {
  return get('/rant/matters/list', query)
}
// 我的任务事项列表
export function myTaskList(query) {
  return get('/rant/matters/myTaskList', query)
}

// 我的督办事项列表
export function myRantList(query) {
  return get('/rant/matters/myRantList', query)
}

// 查看督办事项列表
export function lookList(query) {
  return get('/rant/matters/lookList', query)
}



// 查询督办事项详细
export function getMatters(id) {
  return get('/rant/matters/' + id)
}

// 反馈填报要加载的督办事项信息
export function feedbackDetail(feedbackRecordId) {
  return get('/rant/matters/feedbackDetail/' + feedbackRecordId)
}

// 反馈填报要加载的督办事项信息
export function feedbackRankMatter(id) {
  return get('/rant/matters/feedbackRankMatter/' + id)
}

// 查看督办事项信息
export function lookRankMatter(id) {
  return get('/rant/matters/lookRankMatter/' + id)
}

// 待办查看督办事项信息
export function daiBanLookRankMatter(readTodoNoticeId) {
  return get('/rant/matters/daiBanLookRankMatter/' + readTodoNoticeId)
}

// 待阅转已阅
export function readDaiBanChange(readTodoNoticeId) {
  return post('/rant/matters/readDaiBanChange/' + readTodoNoticeId)
}

// 新增督办事项
export function addMatters(data) {
  return post('/rant/matters', data)
}

// 修改督办事项
export function updateMatters(data) {
  return put('/rant/matters', data)
}

// 删除督办事项
export function delMatters(id) {
  return del('/rant/matters/' + id)
}

// 状态修改
export function changeRantMattersStatus (id, status) {
  const data = {
    id,
    status
  }
  return put('/rant/matters/changeStatus', data)
}

// 反馈填报要加载的督办事项信息
export function feedbackRankMatterList(todoNoticeId) {
  return post('/rant/matters/feedbackRankMatterList/' + todoNoticeId)
}

// 进行中的提交
export function submitIn(data) {
  return post('/rant/matters/submitIn', data)
}

// 批量修改责任人
export function editResponsiblePerson(data) {
  return post('/rant/matters/editResponsiblePerson', data)
}

// 批量修改
export function batchEdit(data) {
  return post('/rant/matters/batchEdit', data)
}
// 批量提交
export function batchSubmit(data) {
  return post('/rant/matters/batchSubmit', data)
}

// 批量删除
export function batchRemove(data) {
  return post('/rant/matters/batchRemove', data)
}

// 获取待办我的吐槽事项详情
export function rantDaiBanInfo(todoNoticeId) {
  return post('/rant/matters/myRant/daiBan/info/' + todoNoticeId)
}

// 新增吐槽
export function myRantAdd(data=null) {
  return post('/rant/matters/myRant/add', data)
}

// 修改吐槽
export function myRantEdit(data=null) {
  return post('/rant/matters/myRant/edit', data)
}

// 吐槽事项初审
export function myRantApprove(data=null) {
  return post('/rant/matters/MyRantApprove', data)
}

// 吐槽事项责任人确认
export function myRantConfirm(data=null) {
  return post('/rant/matters/MyRantConfirm', data)
}

//  待办中心获取督办事项详细信息
export function rantDaiBanDetailForTodoCenter(todoNoticeId) {
  return get('/rant/matters/daiDanInfo/' + todoNoticeId)
}

// 谈话反馈事项确认
export function talkFeedbackConfirm(data={}) {
  return post('/rant/matters/talkFeedbackConfirm', data)
}

//  反馈驳回待办 获取反馈事项信息
export function approveRejectDaiInfo(todoNoticeId) {
  return get('/rant/matters/approveRejectDaiInfo/' + todoNoticeId)
}

//  获取审批待办中督办事项进度集合
export function approveTodoInfo(todoNoticeId) {
  return get('/rant/matters/approveTodoInfo/' + todoNoticeId)
}

// 获取我的吐槽，督办管理审批记录
export function getApproverRecord(mattersId) {
  return get('/rant/matters/getApproveRecord/' + mattersId)
}






