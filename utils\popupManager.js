// popupManager.js
import Vue from 'vue';
import LoginPopup from '@/components/LoginPopup/index.vue'; // 引入登录弹窗组件

const PopupManager = {
  // 使用Vue的extend创建一个组件实例
  instance: null,

  // 显示登录弹窗的方法
  showLoginPopup() {
    
    if (!this.instance) {
      // 如果实例不存在，则创建
      this.instance = new Vue(LoginPopup).$mount();
      this.instance.open();
    
      document.body.appendChild(this.instance.$el);
    }
    this.instance.visible = true;
  },

  // 隐藏登录弹窗的方法
  hideLoginPopup() {
    if (this.instance) {
      this.instance.visible = false;
      setTimeout(() => {
        if (!this.instance.visible) {
          // 确保DOM更新后再移除
          document.body.removeChild(this.instance.$el);
          this.instance = null;
        }
      }, 300); // 延迟移除，模拟动画效果完成
    }
  }
};

export default PopupManager;