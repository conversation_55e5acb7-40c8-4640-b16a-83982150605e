<template>
  <view class="department-picker">
    <uni-popup ref="popup" type="bottom">
      <view class="picker-content">
        <view class="picker-header">
          <text class="cancel-btn" @tap="handleCancel">取消</text>
          <text class="title">{{ title }}</text>
          <!-- <text class="confirm-btn" @tap="handleConfirm">确定</text> -->
          <text class="confirm-btn"></text>
        </view>
        <view class="picker-body">
          <scroll-view scroll-y style="max-height: 60vh;">
            <view v-for="item in visibleOptions" :key="item.id" class="radio-item"
              :style="{ paddingLeft: item.level * 30 + 30 + 'rpx' }">
              <view class="option-label">
                <template v-if="item.children && item.children.length > 0">
                  <text class="expand-btn" @tap.stop="toggleExpand(item)">
                    {{ item.expanded ? '▼' : '▶' }}
                  </text>
                </template>
                <text class="option-text" :class="{ selected: String(selectedValue) === String(item.id) }"
                  @tap="selectDepartment(item)">{{ item.name }}</text>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { listRespdet } from '@/api/rant/respdet'

export default {
  name: 'DepartmentPicker',
  props: {
    // 标题
    title: {
      type: String,
      default: '请选择部门'
    },
    // 自定义选项列表
    customOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      options: [],
      flattenOptions: [],
      visibleOptions: [],
      selectedValue: '',
      selectedItem: null
    }
  },
  created() {
    this.loadDepartments()
  },
  methods: {
    // 加载部门数据
    async loadDepartments() {
      try {
        const res = await listRespdet()
        if (res.data) {
          // 将扁平化数据构建成树形结构
          const treeData = this.buildTreeData(res.data)
          this.options = treeData
          // 扁平化处理数据，用于展示
          this.flattenTreeData(treeData)
        } else {
          uni.showToast({
            title: '获取部门数据失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('获取部门数据失败:', error)
        uni.showToast({
          title: '获取部门数据失败',
          icon: 'none'
        })
      }
    },

    // 构建树形数据结构
    buildTreeData(flatData) {
      const itemMap = new Map()
      const result = []
      // 先统计所有根节点
      const rootIds = flatData.filter(item => item.parentId === 0).map(item => item.id)
      flatData.forEach(item => {
        // 只有第一个根节点展开，其它都收起
        const isFirstRoot = rootIds.length > 0 && item.id === rootIds[0]
        itemMap.set(item.id, { ...item, children: [], expanded: isFirstRoot })
      })
      flatData.forEach(item => {
        const treeItem = itemMap.get(item.id)
        if (item.parentId === 0) {
          result.push(treeItem)
        } else {
          const parent = itemMap.get(item.parentId)
          if (parent) {
            parent.children.push(treeItem)
          } else {
            result.push(treeItem)
          }
        }
      })
      const sortByOrderNum = (items) => {
        items.sort((a, b) => (a.orderNum || 0) - (b.orderNum || 0))
        items.forEach(item => {
          if (item.children && item.children.length > 0) {
            sortByOrderNum(item.children)
          }
        })
      }
      sortByOrderNum(result)
      return result
    },

    // 扁平化处理树形数据，添加层级信息
    flattenTreeData(data) {
      this.flattenOptions = []
      const flatten = (items, level = 0) => {
        items.forEach(item => {
          item.level = level
          this.flattenOptions.push(item)
          if (item.children && item.children.length > 0) {
            flatten(item.children, level + 1)
          }
        })
      }
      flatten(data)
      this.visibleOptions = this.getVisibleNodes(data)
    },

    getVisibleNodes(tree, level = 0) {
      let result = []
      tree.forEach(item => {
        item.level = level
        result.push(item)
        if (item.expanded && item.children && item.children.length > 0) {
          result = result.concat(this.getVisibleNodes(item.children, level + 1))
        }
      })
      return result
    },

    // 设置自定义选项
    setOptions(options) {
      // 判断是否为扁平化数据，如果是则构建树形结构
      const hasParentId = options.some(item => typeof item.parentId !== 'undefined')

      if (hasParentId) {
        // 如果数据包含parentId，说明是扁平化数据，需要构建树形结构
        const treeData = this.buildTreeData(options)
        this.options = treeData
        this.flattenTreeData(treeData)
      } else {
        // 否则认为已经是树形结构
        this.options = options
        this.flattenTreeData(options)
      }
    },

    // 预选择值
    setSelectedValue(value) {
      this.selectedValue = value
      this.selectedItem = this.flattenOptions.find(item => item.id === value)
    },

    // 显示弹窗
    show() {
      // 如果有自定义选项，使用自定义选项
      if (this.customOptions.length > 0) {
        this.setOptions(this.customOptions)
      } else if (this.options.length === 0) {
        // 如果没有加载数据，重新加载
        this.loadDepartments()
      }

      this.$refs.popup.open('bottom')
    },

    // 切换展开/收起
    toggleExpand(item) {
      item.expanded = !item.expanded
      this.visibleOptions = this.getVisibleNodes(this.options)
      console.log(this.visibleOptions)
    },

    // 选中部门
    selectDepartment(item) {
      this.selectedValue = item.id
      this.selectedItem = item
      this.$emit('change', {
        id: this.selectedValue,
        name: this.selectedItem ? this.selectedItem.name : '',
        respPeople: this.selectedItem ? this.selectedItem.respPeople : null,
        respPeopleName: this.selectedItem ? this.selectedItem.respPeopleName : '',
        item: this.selectedItem
      })
      this.handleConfirm()
    },

    // 处理确认
    handleConfirm() {
      if (!this.selectedValue) {
        uni.showToast({
          title: '请选择一项',
          icon: 'none'
        })
        return
      }

      this.$emit('confirm', {
        id: this.selectedValue,
        name: this.selectedItem ? this.selectedItem.name : '',
        respPeople: this.selectedItem ? this.selectedItem.respPeople : null,
        respPeopleName: this.selectedItem ? this.selectedItem.respPeopleName : '',
        item: this.selectedItem
      })
      this.$refs.popup.close()
    },

    // 处理取消
    handleCancel() {
      this.$refs.popup.close()
    }
  }
}
</script>

<style>
.department-picker {
  width: 100%;
}

.picker-content {
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 90rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.cancel-btn,
.confirm-btn {
  font-size: 28rpx;
  padding: 10rpx;
  border-radius: 10rpx;
}

.cancel-btn {
  color: #666666;
}

.confirm-btn {
  /* color: #fff; */
  color: #4080FF;
}

.picker-body {
  padding: 20rpx 0;
}

.radio-item {
  display: block;
  padding: 20rpx 30rpx;
}

.option-label {
  display: flex;
  align-items: center;
}

.option-text {
  font-size: 28rpx;
  color: #333333;
  margin-left: 10rpx;
}

.option-text.selected {
  color: #4080FF;
  font-weight: bold;
  background: #eaf2ff;
  border-radius: 8rpx;
  padding: 2rpx 8rpx;
}

.expand-btn {
  font-size: 28rpx;
  margin-right: 10rpx;
  color: #4080FF;
  width: 32rpx;
  display: inline-block;
  text-align: center;
}
</style>