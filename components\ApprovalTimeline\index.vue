<template>
  <view class="approval-timeline">
    <!-- 审批流程列表 -->
    <view class="timeline-list" v-if="approvalList && approvalList.length > 0">
      <view 
        v-for="(item, index) in approvalList" 
        :key="index" 
        class="timeline-item"
        :class="{ 'is-last': index === approvalList.length - 1 }"
      >
        <!-- 时间线节点 -->
        <view class="timeline-node">
          <view 
            :class="['node-icon', getStatusClass(item.approveStatus)]"
            :style="{ backgroundColor: getStatusColor(item.approveStatus) }"
          >
            <uni-icons 
              :type="getStatusIcon(item.approveStatus)" 
              size="16" 
              color="#fff"
            ></uni-icons>
          </view>
          <view class="node-line" v-if="index !== approvalList.length - 1"></view>
        </view>
        
        <!-- 内容卡片 -->
        <view class="timeline-card">
          <!-- 状态标签 -->
          <view 
            class="status-header" 
            v-if="item.approveTitle !== '发起' && item.approveTitle !== '结束'"
          >
            <view :class="['status-tag', getStatusTagClass(item.approveStatus)]">
              {{ getStatusText(item.approveStatus) }}
            </view>
          </view>
          
          <!-- 用户信息 -->
          <view class="user-section">
            <!-- 头像 -->
            <!-- :style="{ backgroundColor: getAvatarColor(index) }" -->
            <view class="user-avatar">
              {{ getAvatarText(item.approveNickName === null || item.approveNickName === '' ? '系统' : item.approveNickName) }}
            </view>
            
            <!-- 用户详情 -->
            <view class="user-content">
              <!-- 标题行 -->
              <view class="user-header">
                <text class="user-name" v-if="item.approveTitle !== '结束'">{{ item.approveNickName }}</text>
                <text class="user-role" v-if="item.approveTitle">({{ item.approveTitle }})</text>
                <text class="approve-time">{{ item.finishTime || '--'}}</text>
              </view>
              
              <!-- 部门信息 -->
              <view class="dept-info" v-if="item.approveTitle !== '结束'">
                <text class="info-label">部门：</text>
                <text class="info-value">{{ item.approveUserDept || '--' }}</text>
              </view>
              
              <!-- 审批意见 -->
              <view 
                class="opinion-section" 
                v-if="item.approveTitle !== '发起' && item.approveTitle !== '结束'"
              >
                <text class="info-label">审批意见：</text>
                <view class="opinion-content">{{ item.approveDesc || '--' }}</view>
              </view>
            </view>
          </view>
          <view class="timeline-card-split" v-if="index !== approvalList.length - 1"></view>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <uni-icons type="document" size="48" color="#ccc"></uni-icons>
      <text class="empty-text">暂无审批流程数据</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ApprovalTimeline',
  props: {
    approvalList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 头像背景颜色列表
      avatarColors: [
        '#1890ff',
        '#52c41a', 
        '#faad14',
        '#f5222d',
        '#722ed1',
        '#13c2c2',
        '#eb2f96',
        '#fa8c16'
      ]
    }
  },
  methods: {
    // 格式化时间显示
    formatTime(time) {
      if (!time) return '--';
      
      try {
        const date = new Date(time);
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hour = String(date.getHours()).padStart(2, '0');
        const minute = String(date.getMinutes()).padStart(2, '0');
        
        return `${month}-${day} ${hour}:${minute}`;
      } catch (error) {
        return '--';
      }
    },

    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        1: '#faad14', // 审批中 - 橙色
        2: '#52c41a', // 同意 - 绿色 
        3: '#f5222d'  // 驳回 - 红色
      };
      return colorMap[status] || '#52c41a';
    },

    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        1: 'clock',     // 审批中
        2: 'checkmarkempty', // 同意
        3: 'closeempty'      // 驳回
      };
      return iconMap[status] || 'checkmarkempty';
    },

    // 获取状态CSS类
    getStatusClass(status) {
      const classMap = {
        1: 'status-pending',
        2: 'status-approved', 
        3: 'status-rejected'
      };
      return classMap[status] || 'status-default';
    },

    // 获取状态标签CSS类
    getStatusTagClass(status) {
      const classMap = {
        1: 'tag-pending',
        2: 'tag-approved',
        3: 'tag-rejected'
      };
      return classMap[status] || 'tag-default';
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        1: '审批中',
        2: '同意',
        3: '驳回'
      };
      return textMap[status] || '--';
    },

    // 获取头像文本（姓名的最后一个字符）
    getAvatarText(name) {
      if (!name) return '?';
      return name.length > 1 ? name.slice(-2) : name;
    },

    // 获取头像颜色
    getAvatarColor(index) {
      return this.avatarColors[index % this.avatarColors.length];
    }
  }
}
</script>

<style lang="scss" scoped>
.approval-timeline {
  padding: 20rpx 0;
}

.timeline-list {
  position: relative;
}

.timeline-item {
  position: relative;
  display: flex;
  
  &:not(.is-last) {
    // margin-bottom: 40rpx;
  }
}

/* 时间线节点样式 */
.timeline-node {
  position: relative;
  margin-right: 24rpx;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.node-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.node-line {
  width: 2rpx;
  background-color: #e8e8e8;
  flex: 1;
  min-height: 40rpx;
  margin-top: 8rpx;
  z-index: 1;
}

/* 卡片样式 */
.timeline-card {
  flex: 1;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx 0; 
  position: relative;
  min-height: 48rpx;
  
  /* 添加尖角 */
  &::before {
    content: '';
    position: absolute;
    left: -16rpx;
    top: 12rpx;
    width: 0;
    height: 0;
    border-top: 8rpx solid transparent;
    border-bottom: 8rpx solid transparent;
    border-right: 16rpx solid #f8f9fa;
  }
}

/* 状态标签 */
.status-header {
  margin: 0 24rpx 16rpx 24rpx;
}

.status-tag {
  display: inline-block;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  
  &.tag-pending {
    background-color: #fff7e6;
    color: #faad14;
    border: 1rpx solid #ffd591;
  }
  
  &.tag-approved {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1rpx solid #b7eb8f;
  }
  
  &.tag-rejected {
    background-color: #fff2f0;
    color: #f5222d;
    border: 1rpx solid #ffccc7;
  }
}

/* 用户信息区域 */
.user-section {
  display: flex;
  align-items: flex-start;
  margin: 0 24rpx;
}

.user-avatar {
  width: 52rpx;
  height: 52rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 16rpx;
  font-weight: 600;
  margin-right: 20rpx;
  flex-shrink: 0;
  background-color: #1990ff;
}

.user-content {
  flex: 1;
  min-width: 0;
}

.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  flex-wrap: wrap;
}

.user-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-right: 8rpx;
}

.user-role {
  font-size: 24rpx;
  color: #666;
  margin-right: 12rpx;
}

.approve-time {
  font-size: 22rpx;
  color: #999;
  margin-left: auto;
}

/* 信息行 */
.dept-info,
.opinion-section {
  display: flex;
  margin-bottom: 8rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 24rpx;
  color: #333;
  flex: 1;
}

/* 审批意见 */
.opinion-section {
  flex-direction: column;
  margin-top: 12rpx;
}

.opinion-content {
  margin-top: 8rpx;
  padding: 16rpx;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #333;
  line-height: 1.5;
  // border-left: 4rpx solid #1890ff;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.empty-text {
  font-size: 26rpx;
  color: #999;
  margin-top: 20rpx;
}

/* 响应式调整 */
@media screen and (max-width: 400px) {
  .user-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .approve-time {
    margin-left: 0;
    margin-top: 4rpx;
  }
}
.timeline-card-split{
  width: 100%;
  height: 20rpx;
  background-color: #f5f5f5;
  margin-top: 20rpx;
}
</style> 