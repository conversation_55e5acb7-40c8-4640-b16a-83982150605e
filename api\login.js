import { post, get } from '@/utils/request.uni';

// 使用wx.login获取code
const  getWxCode = () => {
  return new Promise((resolve, reject) => {
    wx.getSystemInfo({
      success: (res) => {
        const environment = res.environment;
        if (environment === 'wxwork') {
          // 企业微信环境
          // type 1-企业微信 2-微信
          wx.qy.login({
            success: res => {
              console.log('qy-------------', res, res.code);
              resolve({code: res.code, type: 1});
            },
            fail: err => {
              reject(err);
            }
          });
        } 
        else {
          // 普通微信环境
          if(wx.qy && wx.qy.login){ // 中建通
            wx.qy.login({
              success: res => {
                console.log('wx-------------', res, res.code);
                resolve({code: res.code, type: 3});
              },
              fail: err => {
                reject(err);
              }
            });
          }
          else{ // 日常微信
            wx.login({
              success: res => {
                console.log('wx-------------', res, res.code);
                resolve({code: res.code, type: 2});
              },
              fail: err => {
                reject(err);
              }
            });
          }
          
        }
      }
    });
  });
};

// 通过code获取登录token
const mpWechatLogin = async (data) => {
  return post('/rant/login', data, { headers:{
      'Content-Type': 'application/x-www-form-urlencoded'
    }}, false);
};

// 获取用户详细信息
const getInfo = () => {
  return get('/system/user/getInfo')
}

export {
  getWxCode,
  mpWechatLogin,
  getInfo
};
