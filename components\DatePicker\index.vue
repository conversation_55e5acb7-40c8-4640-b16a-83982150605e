<template>
  <view class="date-picker">
    <view class="date-trigger" @tap="show">
      <text>{{formatDate}}</text>
    </view>
    
    <uni-popup ref="popup" type="bottom">
      <view class="popup-content">
        <view class="popup-header">
          <text class="cancel-btn" @tap="handleCancel">取消</text>
          <text class="title">{{title}}</text>
          <text class="confirm-btn" @tap="handleConfirm">确定</text>
        </view>
        <view class="picker-body">
          <picker 
            mode="date" 
            :value="currentDate"
            :start="startDate"
            :end="endDate"
            @change="handleChange"
            fields="day"
            class="date-picker-view"
          />
          <view class="selected-date">
            <text>{{formatDate}}</text>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  name: 'DatePicker',
  props: {
    title: {
      type: String,
      default: '选择日期'
    },
    // 默认选中的日期，格式：YYYY-MM-DD
    defaultDate: {
      type: String,
      default() {
        const now = new Date()
        return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`
      }
    },
    // 最小日期，格式：YYYY-MM-DD
    startDate: {
      type: String,
      default() {
        const now = new Date()
        return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`
      }
    },
    // 最大日期，格式：YYYY-MM-DD
    endDate: {
      type: String,
      default: '2050-12-31'
    }
  },
  data() {
    return {
      currentDate: ''
    }
  },
  computed: {
    formatDate() {
      if (!this.currentDate) return '请选择日期'
      return this.currentDate
    }
  },
  created() {
    this.currentDate = this.defaultDate
  },
  methods: {
    show() {
      this.$refs.popup.open('bottom')
    },
    // 处理日期变化
    handleChange(e) {
      this.currentDate = e.detail.value
    },
    // 处理取消
    handleCancel() {
      this.$refs.popup.close()
    },
    // 处理确认
    handleConfirm() {
      this.$emit('confirm', this.currentDate)
      this.$refs.popup.close()
    }
  }
}
</script>

<style lang="scss" scoped>
.date-picker {
  width: 100%;
}

.date-trigger {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: left;
  font-size: 32rpx;
  color: #333333;
  padding: 0 20rpx;
  background-color: #FFFFFF;
  border-radius: 8rpx;
}

.popup-content {
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.cancel-btn, .confirm-btn {
  font-size: 28rpx;
  padding: 10rpx;
}

.cancel-btn {
  color: #666666;
}

.confirm-btn {
  color: #4080FF;
}

.picker-body {
  padding: 20rpx 0;
}

.date-picker-view {
  width: 100%;
}

.selected-date {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333333;
  margin-top: 20rpx;
}
</style> 