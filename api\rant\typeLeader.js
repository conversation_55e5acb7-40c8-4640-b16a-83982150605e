import { post, get, put, del } from '@/utils/request.uni';

// 查询督办类型负责人列表
export function listLeader(query) {
  return get('/rant/typeLeader/list', query)
}

// 查询督办类型负责人详细
export function getLeader(id) {
  return get('/rant/typeLeader/' + id)
}

// 新增督办类型负责人
export function addLeader(data) {
  return post('/rant/typeLeader', data)
}

// 修改督办类型负责人
export function updateLeader(data) {
  return put('/rant/typeLeader', data)
}

// 删除督办类型负责人
export function delLeader(id) {
  return del('/rant/typeLeader/' + id)
}
