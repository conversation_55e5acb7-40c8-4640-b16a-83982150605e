<template>
  <view class="select-user-popup" v-if="visible">
    <view class="popup-mask" @tap="handleClose"></view>
    <view class="popup-content">
      <view class="popup-header">
        <text class="popup-title">选择用户</text>
        <text class="close-icon" @tap="handleClose">×</text>
      </view>
      
      <!-- <view class="search-box"> -->
        <view class="search-input-wrapper">
          <input 
            type="text" 
            v-model="searchKey" 
            placeholder="请输入用户名搜索" 
            @confirm="handleSearch"
          />
          <view class="search-btn" @tap="handleSearch" :class="{ disabled: loading }">
            <text v-if="!loading">搜索</text>
            <text v-else>搜索中...</text>
          </view>
        </view>
      <!-- </view> -->
      
      <!-- 已选用户展示区域 -->
      <scroll-view 
        scroll-x 
        class="selected-users" 
        v-if="selectMultiple && selectedUsers.length > 0"
      >
        <view 
          class="selected-user-item" 
          v-for="(item, index) in selectedUsers" 
          :key="'selected-' + (index)"
        >
          <text class="selected-user-name">{{item.nickName || item.userName || '未命名'}}</text>
          <text class="delete-icon" @tap.stop="removeSelectedUser(index)">×</text>
        </view>
      </scroll-view>
      
      <scroll-view 
        scroll-y 
        class="user-list"
        :class="{'with-selected': selectMultiple && selectedUsers.length > 0}"
        @scrolltolower="loadMore"
      >
        <block v-if="userList && userList.length > 0">
          <view 
            class="user-item" 
            v-for="(item, index) in userList" 
            :key="'user-' + (index)"
            @tap="item ? handleSelect(item) : null"
          >
            <template v-if="item">
              <view class="user-info">
                <text class="user-name">{{item.nickName || item.userName || '未命名'}}</text>
                <text class="user-dept">{{item.deptName || ''}}</text>
              </view>
              <view class="select-icon" :class="{ active: isUserSelected(item) }">
                <text v-if="selectMultiple">{{ isUserSelected(item) ? '✓' : '' }}</text>
                <text v-else>{{ selectedUserId === item.userId ? '✓' : '' }}</text>
              </view>
            </template>
            <template v-else>
              <view class="user-info">
                <text class="user-name error">无效的用户数据</text>
              </view>
            </template>
          </view>
        </block>
        
        <view class="empty-tip" v-if="!userList || userList.length === 0 && !loading">
          <text>暂无用户数据</text>
        </view>
        
        <view class="loading-tip" v-if="loading">
          <text>加载中...</text>
        </view>
        
        <view class="no-more-tip" v-if="noMore && userList && userList.length > 0 && !loading">
          <text>没有更多了</text>
        </view>
      </scroll-view>
      
      <!-- 多选模式下的确认按钮 -->
      <view class="confirm-bar" v-if="selectMultiple">
        <view class="selected-count">
          已选择：<text>{{selectedUsers.length}}</text> 人
        </view>
        <view class="confirm-btn" @tap="confirmMultiSelect">确定</view>
      </view>
    </view>
  </view>
</template>

<script>
import { listUser } from '@/api/user'

export default {
  name: 'SelectUser',
  props: {
    roleId: {
      type: String,
      default: ''
    },
    selectMultiple: {
      type: Boolean,
      default: false
    },
    maxSelect: {
      type: Number,
      default: 10
    }
  },
  data() {
    return {
      visible: false,
      searchKey: '',
      userList: [],
      selectedUserId: null,      // 单选模式下选中的用户ID
      selectedUsers: [],         // 多选模式下选中的用户数组
      loading: false,
      noMore: false,
      pageNum: 1,
      pageSize: 100
    }
  },
  methods: {
    show() {
      this.visible = true
      
      // 重置相关数据
      this.searchKey = ''
      this.userList = []
      this.pageNum = 1
      this.noMore = false
      
      // 清除单选模式下的选择
      if (!this.selectMultiple) {
        this.selectedUsers = []
        this.selectedUserId = null
      }
      
      // 加载用户列表
      this.loadUsers()
    },
    
    handleClose() {
      this.visible = false
      this.reset()
    },
    
    reset() {
      this.searchKey = ''
      this.userList = []
      this.selectedUserId = null
      if (!this.selectMultiple) {
        this.selectedUsers = []
      }
      this.pageNum = 1
      this.noMore = false
    },
    
    async loadUsers() {
      if (this.loading || this.noMore) return
      
      this.loading = true
      try {
        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize
        }
        
        if (this.searchKey) {
          params.userName = this.searchKey
        }
        
        if (this.roleId) {
          params.roleId = this.roleId
        }
        
        console.log('Search params:', params)
        
        const res = await listUser(params)
        console.log('Search response:', res)
        
        if (res.code === 200) {
          // 确保rows是一个数组
          const newRows = Array.isArray(res.rows) ? res.rows.filter(item => item) : []
          
          if (this.pageNum === 1) {
            this.userList = newRows
          } else {
            this.userList = [...this.userList, ...newRows]
          }
          
          // 判断是否有更多数据
          const total = res.total || 0
          this.noMore = this.userList.length >= total || newRows.length === 0
          
          // 只有当有数据返回时才增加页码
          if (newRows.length > 0) {
            this.pageNum++
          }
        } else {
          uni.showToast({
            title: res.msg || '获取用户列表失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('加载用户列表失败:', error)
        uni.showToast({
          title: '加载用户列表失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    handleSearch() {
      this.userList = []
      this.pageNum = 1
      this.noMore = false
      this.loadUsers()
    },
    
    loadMore() {
      this.loadUsers()
    },
    
    // 判断用户是否已被选中
    isUserSelected(user) {
      if (!user || typeof user !== 'object' || !user.userId) {
        return false
      }
      
      if (this.selectMultiple) {
        return this.selectedUsers.some(item => item && item.userId === user.userId)
      } else {
        return this.selectedUserId === user.userId
      }
    },
    
    // 处理用户选择
    handleSelect(user) {
      // 如果用户数据无效，不处理
      if (!user || typeof user !== 'object' || !user.userId) {
        console.error('无效的用户数据:', user)
        return
      }
      
      if (this.selectMultiple) {
        // 多选模式
        const index = this.selectedUsers.findIndex(item => item && item.userId === user.userId)
        
        if (index === -1) {
          // 未选中，添加到选中列表
          if (this.selectedUsers.length >= this.maxSelect) {
            uni.showToast({
              title: `最多选择${this.maxSelect}个用户`,
              icon: 'none'
            })
            return
          }
          this.selectedUsers.push({...user}) // 复制一份数据避免引用问题
        } else {
          // 已选中，从选中列表移除
          this.selectedUsers.splice(index, 1)
        }
      } else {
        // 单选模式
        this.selectedUserId = user.userId
        this.$emit('feedbackEmit', {...user}) // 复制一份数据避免引用问题
        this.handleClose()
      }
    },
    
    // 移除已选择的用户
    removeSelectedUser(index) {
      if (index >= 0 && index < this.selectedUsers.length) {
        this.selectedUsers.splice(index, 1)
      }
    },
    
    // 多选模式下确认选择
    confirmMultiSelect() {
     /*  if (this.selectedUsers.length === 0) {
        uni.showToast({
          title: '请至少选择一个用户',
          icon: 'none'
        })
        return
      } */
      
      this.$emit('feedbackEmit', this.selectedUsers)
      this.handleClose()
    }
  }
}
</script>

<style lang="scss" scoped>
.select-user-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.popup-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.close-icon {
  font-size: 40rpx;
  color: #999999;
  padding: 10rpx;
}

.search-box {
  margin-bottom: 20rpx;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  /* height: 80rpx; */
  background: #F5F5F5;
  border-radius: 20rpx;
  padding: 0 30rpx;
}

.search-input-wrapper input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
}

.search-btn {
  padding: 0 20rpx;
  height: 60rpx;
  line-height: 60rpx;
  background: #4080FF;
  color: #FFFFFF;
  font-size: 28rpx;
  border-radius: 30rpx;
  text-align: center;
  min-width: 100rpx;
}

.search-btn.disabled {
  background: #cccccc;
  opacity: 0.6;
}

/* 已选用户显示区域 */
.selected-users {
  display: flex;
  flex-wrap: nowrap;
  white-space: nowrap;
  padding: 10rpx 0;
  margin-bottom: 20rpx;
  max-width: 100%;
  overflow-x: auto;
}

.selected-user-item {
  display: inline-flex;
  align-items: center;
  background-color: #E6EFFF;
  color: #4080FF;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
}

.selected-user-name {
  max-width: 150rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.delete-icon {
  margin-left: 10rpx;
  font-size: 32rpx;
  color: #4080FF;
  line-height: 1;
}

.user-list {
  height: 60vh;
}

.user-list.with-selected {
  height: 50vh; /* 有已选用户时，列表高度减小 */
}

.user-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}

.user-dept {
  font-size: 26rpx;
  color: #999999;
}

.select-icon {
  font-size: 40rpx;
  color: #CCCCCC;
  margin-left: 20rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.select-icon.active {
  color: #4080FF;
}

.empty-tip, .loading-tip, .no-more-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
  color: #999999;
  font-size: 28rpx;
}

.empty-tip {
  height: 200rpx;
}

/* 多选模式下的确认按钮栏 */
.confirm-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0 0;
  margin-top: 20rpx;
  border-top: 1rpx solid #EEEEEE;
}

.selected-count {
  font-size: 28rpx;
  color: #666666;
}

.selected-count text {
  color: #4080FF;
  font-weight: bold;
}

.confirm-btn {
  padding: 0 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: #4080FF;
  color: #FFFFFF;
  font-size: 30rpx;
  border-radius: 40rpx;
  text-align: center;
}

.user-name.error {
  color: #FF4D4F;
}
</style> 