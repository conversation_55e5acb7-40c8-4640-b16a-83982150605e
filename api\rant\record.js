import { post, get, put, del } from '@/utils/request.uni';

// 查询进度反馈记录列表
export function listRecord(query) {
  return get('/rant/record/list', query)
}

// 查询进度反馈记录详细
export function getRecord(id) {
  return get('/rant/record/' + id)
}

// 新增进度反馈记录
export function addRecord(data) {
  return post('/rant/record', data)
}

// 修改进度反馈记录
export function updateRecord(data) {
  return put('/rant/record', data)
}

// 删除进度反馈记录
export function delRecord(id) {
  return del('/rant/record/' + id)
}

// 进度反馈-过程反馈提交接口
export function submitFeedback(data) {
  return post('/rant/record/submitFeedback', data)
}

// 进度反馈-过程反馈提交接口
export function submitTodoFeedback(data) {
  return post('/rant/record/submitTodoFeedback', data)
}

// 过期待办提交反馈记录
export function submitExpireTodoFeedback(data={}) {
  return post('/rant/record/submitExpireTodoFeedback', data)
}

// 过期待办提交反馈记录
export function submitRejectTodoFeedback(data={}) {
  return post('/rant/record/submitRejectTodoFeedback', data)
}

// 进度反馈责任部门负责人审批
export function deptLeaderApprove(data={}) {
  return post('/rant/record/deptLeaderApprove', data)
}

// 事项结项进度反馈管理员审批
export function adminApprove(data={}) {
  return post('/rant/record/adminApprove', data)
}

// 查看审批详情
export function getApproverRecord(id) {
  return get('/rant/record/getApproveRecord/' + id)
}
