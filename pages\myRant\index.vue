<template>
  <view :class="['rant-list-container', { 'popup-open': isPopupOpen }]">
    <!-- 筛选区域 -->
    <view class="filter-area">
      <view class="filter-item" @tap="openTypePopup">
        <text>{{ queryParams.rantClassify || '分类' }}</text>
        <uni-icons type="bottom" size="12"></uni-icons>
      </view>
      <view class="filter-item" @tap="openStatusPopup">
        <text>{{ getStatusLabel(queryParams.status) || '状态' }}</text>
        <uni-icons type="bottom" size="12"></uni-icons>
      </view>
      <view class="search-box">
        <!-- <text class="icon-search">🔍</text> -->
        <uni-icons class="icon-search" type="search" size="20"></uni-icons>
        <input class="search-input" type="text" v-model="queryParams.rantContent" placeholder="搜索内容"
          confirm-type="search" @input="handleSearchInput" @confirm="handleSearch" />
        <text class="clear-icon" v-if="queryParams.rantContent" @tap="clearSearch">×</text>
      </view>
    </view>
    <!--  @refresherrefresh="onRefresh"
      refresher-enabled
      :refresher-triggered="isRefreshing" -->
    <!-- 列表内容  -->
    <scroll-view scroll-y class="rant-scroll-view" refresher-enabled :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh" @scrolltolower="loadMoreData">
      <view class="task-list">
        <view v-for="task in rantList" :key="task.id" class="task-card" @tap="navigateToDetail(task.id)">
          <view class="task-header">
            <!-- 督办类型 -->
            <text class="task-title">{{ getTaskTypeLabel(task.mattersType) || task.title }}</text>
            <!-- 事项状态 -->
            <view :class="['task-status', getStatusClass(task.status)]">{{ getStatusText(task.status) }}</view>
          </view>
          <view class="task-subheader">
            <view class="person-type">
              <!-- 来源 -->
              <!-- <text class="task-person">{{ task.ranterName }}</text> -->
              <!-- 责任人 -->
              <text class="task-person">{{ task.responsiblePersonName || '--' }}</text>
            </view>
            <!-- 分类 -->
            <text class="task-type">{{ task.rantClassify }}</text>
          </view>
          <!-- 督办内容 -->
          <view class="task-content">
            <text>{{ task.rantContent }}</text>
          </view>
          <!-- 如果督办事项状态为按期完成或者延期完成，就显示第四行 左边是结项时间标题，右边是结项日期 -->
          <view v-if="task.status == 2 || task.status == 3" class="task-content cu-flex-between">
            <text>结项时间</text>
            <text>{{ task.closingTime || "--" }}</text>
          </view>
          <view class="item-actions">
            <view v-if="task.status == 0" class="action-btn edit-btn" @tap.stop="editRantItem(task)">编辑</view>
            <view v-if="task.status == 0 || task.status == 7" class="action-btn delete-btn"
              @tap.stop="confirmDelete(task)">删除
            </view>
            <view v-if="task.status == 2 || task.status == 3" class="action-btn edit-btn"
              @tap.stop="handleEvaluation(task)">评价
            </view>
            <view v-if="task.approveFlag == 1" class="action-btn edit-btn" @tap.stop="approvalRecord(task)">审批详情
            </view>
            <!--  <view class="action-btn edit-btn" @tap.stop="handleEvaluation(task)">评价
            </view> -->
          </view>
        </view>
        <!-- 加载更多提示 -->
        <view class="loading-more" v-if="isLoading">
          <view class="loading-icon"></view>
          <text>加载中...</text>
        </view>
        <view class="no-more" v-if="!hasMore && rantList.length > 0">
          <text>没有更多数据了</text>
        </view>
        <view class="empty-list" v-if="!isLoading && rantList.length === 0">
          <text>暂无数据</text>
        </view>
      </view>
    </scroll-view>

    <!-- 创建按钮 -->
    <view class="create-btn" @tap="navigateToCreate">
      <!--      <text class="plus-icon">+</text>-->
      <uni-icons type="plusempty" size="30" :color="'#fff'"></uni-icons>
    </view>

    <!-- 分类选择组件 -->
    <category-picker ref="categoryPopup" title="吐槽分类" dictType="rant_classify" @confirm="applyTypeSelection" :multiple="true"/>
    <!-- 状态筛选弹出层 -->
    <status-picker ref="statusPopup" title="选择状态" dictType="rant_status" @confirm="applyStatusSelection" :multiple="true"/>
    <!-- 删除确认弹出层 -->
    <uni-popup ref="deleteConfirmPopup" type="dialog">
      <uni-popup-dialog type="warn" title="确认删除" content="确定要删除此吐槽吗？此操作不可恢复。" :before-close="false" :show-close="true"
        cancelText="取消" confirmText="确认删除" @confirm="handleDeleteConfirm"
        @close="closeDeleteConfirm"></uni-popup-dialog>
    </uni-popup>

    <!-- 评价弹窗 -->
    <uni-popup ref="evaluationPopup" type="center">
      <view :class="['evaluation-popup', { 'keyboard-active': isTextareaFocused }]">
        <view class="popup-header">
          <text class="popup-title">事项评价</text>
          <text class="close-icon" @tap="closeEvaluationPopup">×</text>
        </view>
        <view class="rating-section">
          <text class="rating-label"><text class="required">*</text>评分</text>
          <view class="star-rating">
            <!--<uni-icons v-for="(star, index) in 5" :key="index" :type="evaluationScore > index ? 'star-filled' : 'star'"
              :size="24" color="#FFCC33" @click="setRating(index + 1)"></uni-icons>-->
          </view>
          <!--<uni-rate allow-half :value="evaluationScore" :is-fill="false" margin="8" size="28" @change="setRating"/>-->
          <RatingStars :value="evaluationScore" :max="5" @set="setRating" />
          <text class="rating-value">{{ evaluationScore * 2 }}.0分</text>
        </view>
        <view class="evaluation-content">
          <text class="content-label"><text class="required">*</text> 评价内容</text>
          <textarea class="evaluation-textarea" v-model="evaluationForm.evaluationContent" placeholder="请输入评价内容"
            maxlength="200" @focus="handleTextareaFocus" @blur="handleTextareaBlur"></textarea>
          <view class="char-count">{{ evaluationForm.evaluationContent.length }}/200</view>
        </view>
        <view class="popup-footer">
          <button class="cancel-btn" @tap="closeEvaluationPopup">取 消</button>
          <button class="submit-btn" v-if="envalatingLoading">
            提交中...
          </button>
          <button v-else class="submit-btn" @tap="submitEvaluation">
            提 交
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import {
  getMatters,
  listMatters,
  addMatters,
  updateMatters,
  delMatters,
  myTaskList,
  myRantList
} from '@/api/rant/matters'
import { getInfo } from '@/api/login'
import { rantStatusOption } from '@/constant/index'
import { getDicts } from '@/api/common'
import CategoryPicker from '@/components/CategoryPicker/index.vue'
import StatusPicker from '@/components/StatusPicker/index.vue'
import { addEvaluative } from '@/api/rant/evaluative'
import RatingStars from '@/components/RatingStars/index.vue'
export default {
  components: {
    CategoryPicker,
    StatusPicker,
    RatingStars
  },
  data() {
    return {
      rantList: [],
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      isLoading: false,
      isRefreshing: false,
      hasMore: true,
      scrollViewHeight: 500, // 默认高度，会在onReady中重新计算
      filterParams: {
        status: '',
        department: '',
        category: ''
      },
      searchText: '',
      selectedCategory: '',
      selectedStatus: '',
      categoryOptions: ['全部', '审批流程', '制度建设', '绩效考核', '办公环境', '福利待遇'],
      statusOptions: [
        { value: 'all', label: '全部' },
        { value: '0', label: '草稿' },
        { value: '1', label: '进行中' },
        { value: '2', label: '按时完成' },
        { value: '3', label: '延期完成' },
        { value: '4', label: '延期未完成' },
        { value: '5', label: '终止' },
        { value: '6', label: '审批中' },
        { value: '7', label: '驳回' }
      ],
      rantToDelete: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ranterId: null,
        // rantObj: null,
        rantClassify: null,
        // planTime: null,
        // deptId: null,
        // responsiblePerson: null,
        // rantContent: null,
        status: null,
        createBy: null,
      },
      selectedTypes: [],
      searchTimeout: null, // 搜索防抖计时器
      types: [
        { dictValue: '', dictLabel: '全部' }
      ],
      // 评价表单
      evaluationForm: {
        rantMattersId: '',
        score: 0,
        evaluationContent: ''
      },
      evaluationVisible: false,
      evaluationScore: 0,
      envalatingLoading: false,
      isTextareaFocused: false,
      isPopupOpen: false, // 控制弹窗打开状态，用于禁止背景滚动
    }
  },
  onReady() {
    // 计算滚动区域高度
    this.calculateScrollViewHeight();
  },
  onShow() {
    // 当页面显示时，如果列表为空则加载第一页数据
    /*  if (this.rantList.length === 0) {
       this.resetAndLoad();
     } */
    getInfo().then((res) => {
      this.queryParams.ranterId = res.user.userId
      this.queryParams.createBy = res.user.userName
      this.ranter = res.user.userId
      this.ranterName = res.user.nickName
      this.resetAndLoad()
    })
    this.fetchTypes();
  },
  /*  onPullDownRefresh() {
     // this.resetAndLoad();
     this.queryParams.pageNum = 1;
     this.hasMore = true;
     this.rantList = [];
     console.log(this.queryParams);
     this.loadData();
     uni.stopPullDownRefresh();
   }, */
  methods: {
    approvalRecord(task) {
      uni.navigateTo({
        url: `/pages/look/approval?id=${task.id}`
      });
    },
    handleTextareaFocus() {
      this.isTextareaFocused = true;
    },
    handleTextareaBlur() {
      this.isTextareaFocused = false;
    },
    /** 设置评分 */
    setRating(value) {
      this.evaluationScore = value
    },

    /** 提交评价 */
    async submitEvaluation() {
      // 防止重复提交
      if (this.envalatingLoading) return;
      if (!this.evaluationScore) {
        uni.showToast({
          icon: 'none',
          title: '请选择评分'
        })
        return
      }
      if (!this.evaluationForm.evaluationContent) {
        uni.showToast({
          icon: 'none',
          title: '请输入评价内容'
        })
        return
      }

      this.envalatingLoading = true;
      this.evaluationForm.score = this.evaluationScore * 2;

      try {
        uni.showLoading({
          title: '提交中...'
        });

        const res = await addEvaluative(this.evaluationForm)
        uni.hideLoading();
        if (res.code === 200) {
          uni.showToast({
            title: '评价成功',
            duration: 2500
          })
          this.isPopupOpen = false; // 恢复背景滚动
          this.$refs.evaluationPopup.close()
          // this.resetAndLoad() // 刷新列表
        } else {
          uni.showToast({
            icon: 'none',
            title: res.msg || '评价失败',
            duration: 1500
          })
        }
      } catch (error) {
        console.error('提交评价失败:', error)
        uni.showToast({
          icon: 'none',
          title: '提交评价失败',
          duration: 1500
        })
      } finally {
        setTimeout(() => {
          this.envalatingLoading = false;
        }, 3500);
        // this.envalatingLoading = false;
        // uni.hideLoading();
      }
    },
    /** 关闭评价弹窗 */
    closeEvaluationPopup() {
      this.isPopupOpen = false; // 恢复背景滚动
      this.$refs.evaluationPopup.close()
    },
    /** 评价按钮操作 */
    handleEvaluation(row) {
      this.evaluationForm = {
        rantMattersId: row.id,
        score: 0,
        evaluationContent: ''
      }
      this.evaluationScore = 0;
      // this.evaluationScore = row.evaluationScore
      this.isPopupOpen = true; // 设置弹窗打开状态，禁止背景滚动
      this.$refs.evaluationPopup.open()
    },

    getStatusText(status) {
      const statusValue = Number(status);
      const statusObj = rantStatusOption.find(s => s.value === statusValue);
      return statusObj ? statusObj.label : status;
    },
    // 获取类型选项
    async fetchTypes() {
      try {
        // 使用getDicts API获取字典数据
        const res = await getDicts('rant_matters_type');
        if (res.code === 200 && res.data) {
          // 添加"全部"选项
          this.types = [{ dictValue: '', dictLabel: '全部' }, ...res.data.map(item => ({
            dictValue: item.dictValue,
            dictLabel: item.dictLabel
          }))];
        }
      } catch (error) {
        console.error('获取类型选项失败:', error);
        uni.showToast({
          icon: 'none',
          title: '获取类型选项失败'
        });
      }
    },
    getTaskTypeLabel(value) {
      if (!value) return '';

      // If value is a comma-separated string (from API), split it into an array
      const typeValues = typeof value === 'string' ? value.split(',') :
        Array.isArray(value) ? value : [value];

      // Map each value to its label
      const typeLabels = typeValues.map(val => {
        const type = this.types.find(t => t.dictValue === val);
        return type ? type.dictLabel : '';
      }).filter(label => label); // Remove empty labels

      return typeLabels.join('|');
    },
    openTypePopup() {
      this.$refs.categoryPopup.show();
    },

    closeTypePopup() {
      this.$refs.categoryPopup.close();
    },

    openStatusPopup() {
      this.$refs.statusPopup.show();
    },

    closeStatusPopup() {
      this.$refs.statusPopup.close();
    },

    closeCategoryPopup() {
      // For backward compatibility, calls closeTypePopup
      this.closeTypePopup();
    },

    statusTextFilter(status) {
      const statusOption = rantStatusOption.find(item => item.value === status);
      return statusOption ? statusOption.label : status;
    },
    getList() {
      this.loading = true
      // 过滤掉queryParams中的null和undefined
      Object.keys(this.queryParams).forEach(key => {
        if (this.queryParams[key] === null || this.queryParams[key] === undefined) {
          delete this.queryParams[key];
        }
      });
      myRantList(this.queryParams).then((response) => {
        this.rantList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    async getUserInfo() {
      const res = await getInfo();
      console.log(res);
    },
    navigateToDetail(id) {
      uni.navigateTo({
        url: `/pages/look/detail?id=${id}&type=myRant&showRanterName=false`
      });
    },
    calculateScrollViewHeight() {
      // 获取系统信息
      const systemInfo = uni.getSystemInfoSync();

      // 获取顶部筛选区域的高度
      const query = uni.createSelectorQuery().in(this);
      query.selectAll('.page-header, .filter-area').boundingClientRect(data => {
        if (data && data.length === 2) {
          // 计算scrollView的高度 = 屏幕高度 - 页面标题高度 - 筛选区域高度 - 底部安全区域 - 额外边距
          const headerHeight = data[0].height;
          const filterHeight = data[1].height;
          const extraMargin = 132; // 底部的额外边距
          this.scrollViewHeight = systemInfo.windowHeight - headerHeight - filterHeight - extraMargin;
        }
      }).exec();
    },
    resetAndLoad() {
      // 重置分页参数并加载第一页
      this.pagination.pageNum = 1;
      this.hasMore = true;
      this.rantList = [];
      this.loadData();
    },
    async loadData() {
      console.log(this.isLoading, this.hasMore);
      if (this.isLoading) return;
      // 如果不是第一页且没有更多数据，则返回
      if (this.pagination.pageNum > 1 && !this.hasMore) return;

      this.isLoading = true;
      uni.showLoading({
        title: '加载中...',
        mask: true
      });
      try {
        const params = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          // status: this.selectedStatus === '全部' ? '' : this.selectedStatus || '',
          category: this.selectedCategory === '全部' ? '' : this.selectedCategory || '',
          rantContent: this.queryParams.rantContent || '',
          // ...this.filterParams,
          createBy: this.queryParams.createBy,
          ranterId: this.queryParams.ranterId,
          rantClassify: this.queryParams.rantClassify || '',
          status: this.queryParams.status || ''
        };

        // 过滤掉params中的null和undefined
        Object.keys(params).forEach(key => {
          if (params[key] === null || params[key] === undefined) {
            delete params[key];
          }
        });
        const response = await myRantList(params);

        if (response.code === 200) {
          const { rows, total } = response;

          // 更新列表和分页信息
          if (this.pagination.pageNum === 1) {
            this.rantList = rows;
          } else {
            this.rantList = [...this.rantList, ...rows];
          }

          this.pagination.total = total;
          // 判断是否还有更多数据
          this.hasMore = this.rantList?.length < total;
          console.log(this.rantList?.length, total);
        }
        else {
          uni.showToast({
            title: response.msg || '加载失败',
            icon: 'none'
          });
        }
      }
      catch (error) {
        console.error('Load data error:', error);
        uni.showToast({
          title: '网络异常，请稍后再试',
          icon: 'none'
        });
      }
      finally {
        this.isLoading = false;
        this.isRefreshing = false;
        uni.hideLoading();
      }
    },
    loadMoreData() {
      if (this.hasMore && !this.isLoading) {
        this.pagination.pageNum += 1;
        this.loadData();
      }
    },

    /** 下拉刷新 */
    onRefresh() {
      this.isRefreshing = true;
      this.resetAndLoad();
    },

    /** 重置分页参数和列表 */
    resetPagination() {
      this.pagination.pageNum = 1;
      this.hasMore = true;
      this.rantList = [];
    },
    onSearch() {
      this.resetAndLoad();
    },
    navigateToCreate() {
      uni.navigateTo({
        url: '/pages/myRant/create'
      });
    },
    getStatusClass(status) {
      const statusValue = Number(status);
      const statusObj = rantStatusOption.find(s => s.value === statusValue);

      if (!statusObj) return '';

      switch (statusValue) {
        case 0: // 草稿
          return 'status-draft';
        case 1: // 进行中
          return 'status-in-progress';
        case 2: // 按时完成
          return 'status-on-time';
        case 3: // 延期完成
          return 'status-delayed-finished';
        case 4: // 延期未完成
          return 'status-delayed-unfinished';
        case 5: // 终止
          return 'status-ended';
        case 6: // 审批中
          return 'status-approving';
        case 7: // 驳回
          return 'status-rejected';
        default:
          return '';
      }
    },
    // 分类筛选相关
    selectCategory(category) {
      this.selectedCategory = category;
      this.closeTypePopup();
      // 重新加载数据
      this.resetAndLoad();
    },
    resetCategorySelection() {
      this.selectedCategory = '';
    },
    applyCategorySelection() {
      this.closeTypePopup();
      this.resetAndLoad();
    },
    // 状态筛选相关
    toggleStatusSelection(status) {
      if (this.selectedStatus === status) {
        this.selectedStatus = '';
      } else {
        this.selectedStatus = status;
      }
    },
    resetStatusSelection() {
      this.selectedStatus = '';
    },
    applyStatusSelection(status) {
      this.selectedStatus = status.value;
      this.queryParams.status = status.value;
      this.resetPagination();
      this.loadData();
    },
    // 编辑功能
    editRantItem(item) {
      uni.navigateTo({
        url: `/pages/myRant/create?id=${item.id}&type=update`
      });
    },
    // 删除功能
    confirmDelete(item) {
      this.rantToDelete = item;
      this.$refs.deleteConfirmPopup.open();
    },
    closeDeleteConfirm() {
      this.rantToDelete = null;
    },
    async handleDeleteConfirm() {
      if (!this.rantToDelete) return;

      // 显示加载提示
      uni.showLoading({
        title: '删除中...',
        mask: true
      });

      try {
        const res = await delMatters([this.rantToDelete.id]);

        // 隐藏加载提示
        uni.hideLoading();

        if (res.code === 200) {
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          });

          this.resetAndLoad();
        } else {
          throw new Error(res.msg || '删除失败');
        }
      } catch (error) {
        // 隐藏加载提示
        uni.hideLoading();

        console.error('删除失败:', error);
        uni.showToast({
          title: error.message || '删除失败',
          icon: 'none'
        });
      } finally {
        this.closeDeleteConfirm();
      }
    },
    // 处理搜索输入（带防抖）
    handleSearchInput() {
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout);
      }

      this.searchTimeout = setTimeout(() => {
        this.resetPagination();
        this.loadData();
      }, 500); // 500ms防抖
    },

    // 处理搜索
    handleSearch() {
      this.resetPagination();
      this.loadData();
    },

    // 清除搜索
    clearSearch() {
      this.queryParams.rantContent = '';
      this.resetPagination();
      this.loadData();
    },

    getStatusLabel(value) {
      if (!value && value !== 0) return '状态';
      const status = rantStatusOption.find(s => s.value === Number(value));
      return status ? status.label : '状态';
    },

    getSelectedTypesLabel() {
      if (!this.selectedTypes || this.selectedTypes.length === 0) {
        return '分类';
      }

      const typeLabels = this.selectedTypes.map(typeValue => {
        const type = this.categoryOptions.find(t => t === typeValue);
        return type ? type : '';
      }).filter(label => label);

      if (typeLabels.length === 0) {
        return '分类';
      }

      if (typeLabels.length > 1) {
        return `已选${typeLabels.length}项`;
      }

      return typeLabels[0];
    },

    // 处理类型选择变更
    handleTypeChange(e) {
      const selectedValues = e.detail.value;
      const allOption = this.categoryOptions.find(t => t === '');
      const normalTypes = this.categoryOptions.filter(t => t !== '');

      // 检查是否包含"全部"选项
      const hasAll = selectedValues.includes('');

      if (hasAll) {
        // 如果选择了"全部"，则选中所有选项（包括"全部"）
        this.selectedTypes = ['', ...normalTypes.map(type => type)];
      } else {
        // 如果没有选择"全部"，只使用选中的具体选项
        this.selectedTypes = selectedValues;

        // 检查是否手动选中了所有具体选项
        const allNormalTypesSelected = normalTypes.every(type =>
          selectedValues.includes(type)
        );

        if (allNormalTypesSelected) {
          // 如果选中了所有具体选项，自动选中"全部"
          this.selectedTypes = ['', ...selectedValues];
        }
      }
    },

    // 重置类型选择
    resetTypeSelection() {
      this.selectedTypes = [];
    },

    // 应用类型选择
    applyTypeSelection(selectedTypes) {
      console.log('applyTypeSelection---------', selectedTypes)
      // 如果选中了所有选项，则使用空数组（表示全部）
      if (this.selectedTypes.length === this.categoryOptions.length) {
        this.queryParams.rantClassify = '';
      } else {
        this.queryParams.rantClassify = this.selectedTypes.join(',');
      }
      this.queryParams.rantClassify = selectedTypes;
      // this.closeTypePopup();
      this.resetPagination();
      this.loadData();
    },

    // 处理状态选择变更
    handleStatusChange(e) {
      const value = e.detail.value;
      if (value === 'all') {
        this.queryParams.status = '';
      } else {
        this.queryParams.status = Number(value);
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.keyboard-active {
  margin-bottom: 50vh;
}

.rant-list-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #D6E1F1;
}

/* 弹窗打开时禁止背景滚动 */
.popup-open {
  overflow: hidden !important;
  height: 100vh !important;
}

.filter-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #D6E1F1;
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.filter-item {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  background-color: #FFFFFF;
  border-radius: 32rpx;
  margin-right: 16rpx;
  font-size: 24rpx;
  color: #333333;
  border: 1rpx solid #E8E8E8;
}

.filter-item:active {
  background-color: #F0F0F0;
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 0 20rpx;
  height: 60rpx;
  border: 1rpx solid #E8E8E8;
}

.icon-search {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
}

.clear-icon {
  font-size: 32rpx;
  color: #999;
  padding: 0 10rpx;
}

.rant-scroll-view {
  position: fixed;
  top: 100rpx;
  /* filter-area的高度 */
  left: 0;
  right: 0;
  bottom: 0;
}

.task-list {
  padding: 20rpx 24rpx 0 24rpx;
}

.simple-list {
  padding: 0;
}

.simple-item {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.required {
  color: #f56c6c;
  margin-right: 5rpx;
}

.item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.item-department {
  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
}

.item-date {
  font-size: 26rpx;
  color: #999;
}

.item-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.item-category {
  font-size: 26rpx;
  color: #666;
  margin-right: 16rpx;
  // background-color: rgba(0, 0, 0, 0.05);
  // padding: 4rpx 16rpx;
  // border-radius: 20rpx;
}

.item-content {
  font-size: 28rpx;
  color: #999999;
  line-height: 1.4;
  margin-top: 10rpx;
  /* 文本超出三行显示省略号 */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #eee;
}

.action-btn {
  padding: 6rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  margin-left: 16rpx;
}

.edit-btn {
  background-color: #4080FF;
  color: white;
}

.delete-btn {
  background-color: #FF4D4F;
  color: white;
}

.loading-more,
.no-more,
.empty-list {
  text-align: center;
  padding: 30rpx 0;
  color: #999999;
  font-size: 24rpx;
}

.loading-icon {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #4080FF;
  border-radius: 50%;
  margin-right: 10rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.create-btn {
  position: fixed;
  right: 40rpx;
  bottom: 80rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #4080FF;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
  z-index: 999;
}

.plus-icon {
  color: #FFFFFF;
  font-size: 60rpx;
  font-weight: 300;
  display: inline-block;
  margin-bottom: 20rpx;
}

/* 弹出层样式 */
.popup-content {
  background-color: #FFFFFF;
  border-radius: 16rpx 16rpx 0 0;
  padding: 30rpx;
}

.popup-title {
  font-weight: 500;
  font-size: 30rpx;
  color: #333333;
  text-align: center;
  margin-bottom: 30rpx;
}

.popup-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.popup-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
  font-size: 28rpx;
  color: #333333;
}

.popup-item .selected {
  color: #4080FF;
  font-weight: 500;
}

.popup-footer {
  margin-top: 30rpx;
  display: flex;
  justify-content: space-between;
}

.popup-btn {
  width: 30%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #F5F5F5;
  color: #333333;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
}

.checkbox {
  width: 20rpx;
  height: 20rpx;
  border: 1rpx solid #999;
  border-radius: 4rpx;
  margin-right: 10rpx;
}

.checkbox-selected {
  background-color: #4080FF;
}

.popup-btn-reset {
  background-color: #F5F5F5;
  color: #333333;
}

.popup-btn-cancel {
  background-color: #F5F5F5;
  color: #333333;
}

.popup-btn-confirm {
  background-color: #4080FF;
  color: white;
}

.radio-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
  font-size: 28rpx;
  color: #333333;
}

.checkbox-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
  font-size: 28rpx;
  color: #333333;
}

.checkbox-item .selected,
.radio-item .selected {
  color: #4080FF;
  font-weight: bold;
}

/* 评价弹窗样式 */
.evaluation-popup {
  width: 650rpx;
  background-color: #fff;
  border-radius: 10rpx;
  overflow: hidden;
}

.popup-header {
  position: relative;
  padding: 30rpx;
  text-align: center;
  border-bottom: 1px solid #eee;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
}

.close-icon {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  font-size: 40rpx;
  color: #999;
}

.rating-section {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.rating-label {
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.star-rating {
  display: flex;
  gap: 20rpx;
  margin-bottom: 10rpx;
}

.rating-value {
  font-size: 28rpx;
  color: #666;
  margin-top: 10rpx;
}

.evaluation-content {
  padding: 0 30rpx 30rpx;
  position: relative;
}

.evaluation-textarea {
  width: 100%;
  height: 200rpx;
  border: 1px solid #dcdfe6;
  border-radius: 4rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.popup-footer {
  margin-top: 30rpx;
  display: flex;
  justify-content: space-between;
}

.popup-btn {
  width: 30%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #F5F5F5;
  color: #333333;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.popup-btn-cancel {
  background-color: #F5F5F5;
}

.popup-btn-reset {
  background-color: #F5F5F5;
}

.popup-btn-confirm {
  background-color: #4080FF;
  color: #FFFFFF;
}

.char-count {
  position: absolute;
  right: 40rpx;
  bottom: 40rpx;
  font-size: 24rpx;
  color: #999;
}

.evaluation-content {
  padding: 0 30rpx 30rpx;
  position: relative;
}

.content-label {
  display: block;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.cancel-btn,
.submit-btn {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 30rpx;
  border: none;
  border-radius: 0;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #333;
}

.submit-btn {
  background-color: #1890ff;
  color: #fff;
}

.submit-btn:disabled {
  background-color: #d9d9d9;
  color: #999;
  cursor: not-allowed;
}
</style>
