<!-- Layout.vue -->
<template>
    <view class="layout">
      <!-- 将登录弹窗组件放置于此，初始隐藏 -->
      <LoginPopup ref="loginPopup" />
      
      <!-- 页面内容插槽 -->
      <slot></slot>
    </view>
  </template>
  
  <script>
  import LoginPopup from '@/components/LoginPopup';
  import {handleLogin} from '@/utils/utils.js'
  export default {
    components: {
      LoginPopup
    },
    data() {
      return {
        isLoginPopupVisible: false
      };
    },
    created() {
      uni.$on('loginExpired', this.showLoginPopup);
    },
    methods: {
      // 控制登录弹窗显示的方法
      showLoginPopup() {
        console.log('showLoginPopup-----------');
        handleLogin();
        // 打开登录弹窗
        // this.$refs.loginPopup.openPopup();
      },
      // 控制登录弹窗隐藏的方法
      hideLoginPopup() {
        this.isLoginPopupVisible = false;
      }
    }
  };
  </script>
