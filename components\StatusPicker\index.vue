<template>
  <view class="status-picker">
    <uni-popup ref="popup" type="bottom">
      <view class="picker-content">
        <view class="picker-header">
          <text class="title">{{title}}</text>
        </view>
        <view class="picker-body">
          <!-- 单选模式 -->
          <radio-group v-if="!multiple" @change="handleChange">
            <label 
              v-for="status in options" 
              :key="status.value" 
              class="radio-item"
            >
              <view class="option-label">
                <radio 
                  :value="status.value" 
                  :checked="String(selectedValue) === String(status.value) || (status.value === 'all' && !selectedValue)" 
                  color="#4080FF" 
                />
                <text class="option-text">{{status.label}}</text>
              </view>
            </label>
          </radio-group>
          
          <!-- 多选模式 -->
          <view v-else>
            <label 
              v-for="status in options" 
              :key="status.value" 
              class="radio-item"
              @tap="handleMultipleChange(status.value)"
            >
              <view class="option-label">
                <checkbox 
                  :value="status.value" 
                  :checked="isChecked(status.value)" 
                  color="#4080FF" 
                />
                <text class="option-text">{{status.label}}</text>
              </view>
            </label>
          </view>
        </view>
        <view class="picker-footer">
          <view class="footer-btn reset-btn" @tap="handleReset">重置</view>
          <view class="footer-btn cancel-btn" @tap="handleCancel">取消</view>
          <view class="footer-btn confirm-btn" @tap="handleConfirm">确定</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  name: 'StatusPicker',
  props: {
    // 标题
    title: {
      type: String,
      default: '选择状态'
    },
    // 选项列表
    options: {
      type: Array,
      default: () => [
      {label: '全部', value: '', type: null},
      {label: '草稿', value: 0, type: null},
      {label: '进行中', value: 1, type: 'info', color: 'yellow'},
      {label: '按时完成', value: 2, type: 'success'},
      {label: '延期完成', value: 3, type: 'danger'},
      {label: '延期未完成', value: 4, type: 'success', color: 'red', fontColor: 'white!important'},
      {label: '终止', value: 5, type: 'info'},
      {label: '审批中', value: 6, type: 'warning'},
      {label: '驳回', value: 7,  type: 'success', color: 'red', fontColor: 'white!important'},
      ]
    },
    // 默认选中值
    value: {
      type: [String, Number, Array],
      default: ''
    },
    // 是否支持多选
    multiple: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedValue: this.multiple ? (Array.isArray(this.value) ? [...this.value] : []) : this.value,
      originalValue: this.multiple ? (Array.isArray(this.value) ? [...this.value] : []) : this.value // 存储原始值，用于取消操作时恢复
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (this.multiple) {
          this.selectedValue = Array.isArray(newVal) ? [...newVal] : []
          this.originalValue = Array.isArray(newVal) ? [...newVal] : []
        } else {
          this.selectedValue = newVal
          this.originalValue = newVal
        }
      },
      immediate: true
    }
  },
  methods: {
    // 显示弹窗
    show() {
      // 打开弹窗时保存当前值，用于取消时恢复
      if (this.multiple) {
        this.originalValue = Array.isArray(this.selectedValue) ? [...this.selectedValue] : []
      } else {
        this.originalValue = this.selectedValue
      }
      this.$refs.popup.open('bottom')
    },
    
    // 处理单选变更
    handleChange(e) {
      const value = e.detail.value
      this.selectedValue = value
      this.$emit('change', {
        value: this.selectedValue,
        label: this.options.find(item => String(item.value) === String(this.selectedValue))?.label
      })
    },
    
    // 处理多选变更
    handleMultipleChange(value) {
      if (!Array.isArray(this.selectedValue)) {
        this.selectedValue = []
      }
      
      // 获取"全部"选项的值（通常是空字符串或'all'）
      const allValue = this.options.find(item => item.value === '' || item.value === 'all')?.value || '';
      
      // 获取除"全部"外的所有项
      const allItems = this.options.filter(item => item.value !== allValue);
      const allItemValues = allItems.map(item => item.value);
      
      // 检查点击的是否是"全部"选项
      const wasAllSelected = this.selectedValue.includes(allValue);
      const clickedAll = String(value) === String(allValue);
      
      let newSelectedValues = [...this.selectedValue];
      
      if (clickedAll) {
        // 点击了"全部"选项
        if (wasAllSelected) {
          // 取消选中"全部"，同时取消选中所有子项
          newSelectedValues = [];
        } else {
          // 选中"全部"，同时选中所有子项
          newSelectedValues = [allValue, ...allItemValues];
        }
      } else {
        // 点击了其他选项
        const index = newSelectedValues.findIndex(item => String(item) === String(value));
        if (index > -1) {
          // 如果已选中，则取消选中
          newSelectedValues.splice(index, 1);
          // 取消选中任何子项时，也要取消选中"全部"
          newSelectedValues = newSelectedValues.filter(v => String(v) !== String(allValue));
        } else {
          // 如果未选中，则添加到选中列表
          newSelectedValues.push(value);
          
          // 检查除"全部"外的所有子项是否都被选中
          const allOthersSelected = allItemValues.every(val => 
            newSelectedValues.includes(val) || String(newSelectedValues).includes(String(val))
          );
          
          if (allOthersSelected && !newSelectedValues.includes(allValue)) {
            // 如果所有子项都被选中，"全部"也应被选中
            newSelectedValues.push(allValue);
          }
        }
      }
      
      this.selectedValue = newSelectedValues;
      
      // 获取选中项的标签
      const selectedLabels = this.selectedValue.map(val => {
        const option = this.options.find(item => String(item.value) === String(val))
        return option ? option.label : val
      })
      
      // 处理返回数据 - 如果选中了"全部"，返回空数组
      let returnValues = [...this.selectedValue];
      if (this.selectedValue.includes(allValue)) {
        returnValues = [];
      } else {
        returnValues = this.selectedValue.filter(v => String(v) !== String(allValue));
      }
      
      const returnLabels = returnValues.length === 0 && this.selectedValue.includes(allValue) ? ['全部'] : selectedLabels.filter(label => {
        const option = this.options.find(item => item.label === label);
        return !option || String(option.value) !== String(allValue);
      });
      
      this.$emit('change', {
        value: returnValues,
        label: returnLabels.join(', '),
        selectedItems: returnValues.map(val => {
          return this.options.find(item => String(item.value) === String(val))
        }).filter(Boolean)
      })
    },
    
    // 检查多选项是否被选中
    isChecked(value) {
      if (!this.multiple || !Array.isArray(this.selectedValue)) {
        return false
      }
      return this.selectedValue.some(item => String(item) === String(value))
    },
    
    // 处理重置
    handleReset() {
      if (this.multiple) {
        this.selectedValue = []
        this.$emit('change', {
          value: [],
          label: '',
          selectedItems: []
        })
      } else {
        this.selectedValue = ''
        this.$emit('change', {
          value: '',
          label: '全部'
        })
      }
    },
    
    // 处理确认
    handleConfirm() {
      if (this.multiple) {
        // 获取"全部"选项的值
        const allValue = this.options.find(item => item.value === '' || item.value === 'all')?.value || '';
        
        // 处理返回数据
        let returnValues = [...this.selectedValue];
        
        // 如果选中了"全部"选项，返回空数组（表示全部）
        if (this.selectedValue.includes(allValue)) {
          returnValues = [];
        } else {
          // 过滤掉"全部"选项（如果有的话）
          returnValues = this.selectedValue.filter(v => String(v) !== String(allValue));
        }
        
        const selectedLabels = returnValues.length === 0 ? ['全部'] : returnValues.map(val => {
          const option = this.options.find(item => String(item.value) === String(val))
          return option ? option.label : val
        })
        
        this.$emit('confirm', {
          value: returnValues,
          label: selectedLabels.join(', '),
          selectedItems: returnValues.length === 0 ? [] : returnValues.map(val => {
            return this.options.find(item => String(item.value) === String(val))
          }).filter(Boolean)
        })
      } else {
        this.$emit('confirm', {
          value: this.selectedValue,
          label: this.options.find(item => String(item.value) === String(this.selectedValue))?.label
        })
      }
      this.$refs.popup.close()
    },
    
    // 处理取消
    handleCancel() {
      // 恢复到打开弹窗前的选择状态
      if (this.multiple) {
        this.selectedValue = Array.isArray(this.originalValue) ? [...this.originalValue] : []
      } else {
        this.selectedValue = this.originalValue
      }
      this.$refs.popup.close()
    }
  }
}
</script>

<style>
.status-picker {
  width: 100%;
}

.picker-content {
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}

.picker-header {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 90rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.picker-body {
  padding: 20rpx 0;
  max-height: 60vh;
  overflow-y: auto;
}

.radio-item {
  display: block;
  padding: 20rpx 30rpx;
}

.option-label {
  display: flex;
  align-items: center;
}

.option-text {
  font-size: 28rpx;
  color: #333333;
  margin-left: 10rpx;
}

.picker-footer {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #EEEEEE;
}

.footer-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  border-radius: 8rpx;
  margin: 0 10rpx;
}

.reset-btn {
  background-color: #F5F5F5;
  color: #666666;
}

.cancel-btn {
  background-color: #F5F5F5;
  color: #666666;
}

.confirm-btn {
  background-color: #4080FF;
  color: #FFFFFF;
}
</style> 