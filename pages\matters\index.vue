<template>
  <view class="task-container">
    <!-- Filter Area -->
    <view class="filter-area">
      <view class="filter-item" @tap="openCategoryPopup">
        <text>{{ getSelectedCategoryLabel() || '类型' }}</text>
        <uni-icons type="bottom" :size="12"></uni-icons>
      </view>
      <view class="filter-item" @tap="openStatusPopup">
        <text>{{ getStatusLabel(queryParams.status) || '状态' }}</text>
        <uni-icons type="bottom" :size="12"></uni-icons>
      </view>
      <view class="search-box">
        <!-- <text class="icon-search">🔍</text> -->
        <uni-icons class="icon-search" type="search" size="20"></uni-icons>
        <input class="search-input" type="text" v-model="queryParams.rantContent" placeholder="搜索内容"
          confirm-type="search" @input="handleSearchInput" @confirm="handleSearch" />
        <text class="clear-icon" v-if="queryParams.rantContent" @tap="clearSearch">×</text>
      </view>
    </view>

    <!-- Task List with scroll-view -->
    <scroll-view class="scroll-container" scroll-y="true" refresher-enabled="true" :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh" @scrolltolower="onLoadMore" refresher-threshold="100" :lower-threshold="100">
      <view class="task-list">
        <view v-for="(task, index) in taskList" :key="index" class="task-card" @tap="navigateToDetail(task.id)">
          <view class="task-header">
            <!-- 督办类型 -->
            <text class="task-title">{{ index + 1 }}.{{ getTaskTypeLabel(task.mattersType) || task.title || '--' }}</text>
            <!-- 事项状态 -->
            <text :class="['task-status', getStatusClass(task.status)]">{{ getStatusText(task.status) }}</text>
          </view>
          <view class="task-subheader">
            <view class="person-type">
              <!-- 来源 -->
              <!-- <text class="task-person">{{task.ranterName || '--'}}</text> -->
              <!-- 责任人 -->
              <text class="task-person">{{ task.responsiblePersonName || '--' }}</text>

              <!-- <text class="task-person">{{task.responsiblePersonName || task.person}}</text> -->
              <!-- <text class="task-type">{{task.rantClassify || task.type}}</text> -->
            </view>
            <!-- 分类 -->
            <text class="task-type">{{ task.rantClassify }}</text>
          </view>
          <!-- 督办内容 -->
          <view class="task-content">
            <text>{{ task.rantContent }}</text>
          </view>
          <!-- 如果督办事项状态为按期完成或者延期完成，就显示第四行 左边是结项时间标题，右边是结项日期 -->
          <view v-if="task.status == 2 || task.status == 3" class="task-content cu-flex-between">
            <text>结项时间</text>
            <text>{{ task.closingTime || "--" }}</text>
          </view>
          <view class="item-actions">
            <view class="action-btn edit-btn" @tap.stop="editRantItem(task)"
              v-if="task.status == 1 || task.status == 4 || task.status == 0">编辑</view>
            <view class="action-btn end-btn" @tap.stop="endRantItem(task)" v-if="task.status == 5">启用</view>
            <view class="action-btn end-btn" @tap.stop="endRantItem(task)" v-if="task.status == 1 || task.status == 4">
              终止</view>
            <view class="action-btn delete-btn" @tap.stop="confirmDelete(task)" v-if="task.status == 0">删除</view>
            <view class="action-btn edit-btn" @tap.stop="approvalRecord(task)" v-if="task.approveFlag == 1">审批详情</view>
          </view>
        </view>

        <!-- Loading & Empty States -->
        <view class="empty-state" v-if="!loading && taskList.length === 0">
          <text>暂无督办事项</text>
        </view>

        <view class="no-more" v-if="isEnd && taskList.length > 0">
          <text>没有更多数据了</text>
        </view>
      </view>
    </scroll-view>

    <!-- 创建按钮 -->
    <view class="create-btn" @touchstart="touchStart" @touchmove="touchMove" @tap="handleCreateBtn">
      <!--      <text class="plus-icon">+</text>-->
      <uni-icons type="plusempty" size="30" :color="'#fff'"></uni-icons>
    </view>

    <category-picker ref="categoryPicker" :multiple="true" title="选择类型" dictType="rant_matters_type"
      @confirmWithDetail="handleCategorySelect" />

    <!-- Status Popup -->
    <StatusPicker ref="statusPicker" title="选择状态" @confirm="handleStatusSelect" :multiple="true"/>
    <!-- 删除确认弹出层 -->
    <uni-popup ref="deleteConfirmPopup" type="dialog">
      <uni-popup-dialog type="warn" title="确认删除" content="确定要删除此督办事项吗？此操作不可恢复。" :before-close="false" :show-close="true"
        cancelText="取消" confirmText="确认删除" @confirm="handleDeleteConfirm"
        @close="closeDeleteConfirm"></uni-popup-dialog>
    </uni-popup>
    <!-- 确认要"启用""新增督办事项，用于测试25号推送定时任务"此督办事项吗？ -->
    <!-- 终止确认弹出层 -->
    <uni-popup ref="endConfirmPopup" type="dialog">
      <uni-popup-dialog type="info" :title="popupTitle" :content="popupContent" :before-close="false" :show-close="true"
        cancelText="取消" confirmText="确认" @confirm="handleEndConfirm" @close="closeEndConfirm"></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
import { listMatters, delMatters, changeRantMattersStatus } from '@/api/rant/matters.js';
import { getDicts } from '../../api/common.js';
import { rantStatusOption } from '@/constant';
import { getStatusClass } from '@/utils/utils';
import CategoryPicker from '@/components/CategoryPicker/index.vue'
import StatusPicker from '@/components/StatusPicker/index.vue'
import mixin from '@/mixins/mixin';
export default {
  mixins: [mixin],
  components: {
    CategoryPicker,
    StatusPicker
  },
  data() {
    return {
      popupTitle: '',
      popupContent: '',
      changeStatus: '',
      taskList: [],
      categories: [],
      statuses: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        rantContent: '',
        rantClassify: '',
        status: '',
        mattersType: [],
        responsiblePerson: '',
        ranter: ''
      },
      selectedCategory: '',
      selectedStatus: '',
      sortOrder: '',
      loading: false,
      refreshing: false,
      rantToDelete: null,
      itemToEnd: null,
      total: 0,
      filterType: 'all', // 'all', 'myResponsibility', 'myRant'
      dictTypes: ['rant_matters_type', 'rant_classify'], // 数据字典类型
      isEnd: false, // 是否到底部
      searchTimeout: null,
      mattersTypeOptions: [],
      // 按钮拖拽相关
      btnPosition: {
        x: 30,
        y: 30
      },
      isTouching: false,
      end: false
    }
  },
  onLoad() {
    this.initDictData();
  },
  onShow() {
    console.log('onShow---');
    this.taskList = [];
    this.loadTaskList();
  },

  methods: {
    // scroll-view 下拉刷新
    onRefresh() {
      this.refreshing = true;
      this.queryParams.pageNum = 1;
      this.isEnd = false;
      this.taskList = [];
      this.loadData().finally(() => {
        this.refreshing = false;
      });
    },

    // scroll-view 上拉加载更多
    onLoadMore() {
      if (this.isEnd || this.loading) {
        return;
      }
      this.queryParams.pageNum++;
      this.loadData();
    },

    approvalRecord(task) {
      uni.navigateTo({
        url: `/pages/look/approval?id=${task.id}`
      });
    },
    handleCategorySelect(category) {
      console.log('handleCategorySelect---', category.values);
      this.queryParams.mattersType = category.values;
      this.queryParams.pageNum = 1;
      this.isEnd = false;
      this.taskList = [];
      this.loadData();
    },
    handleStatusSelect(status) {
      this.queryParams.status = status.value;
      this.queryParams.pageNum = 1;
      this.isEnd = false;
      this.taskList = [];
      this.loadData();
    },
    // 处理状态选择变更
    handleStatusChange(e) {
      const value = e.detail.value;
      if (value === 'all') {
        this.queryParams.status = '';
      } else {
        this.queryParams.status = Number(value);
      }
    },
    getMattersTypeText(types) {
      if (!types) return '';
      const typeArray = types.split(',');
      const typeTexts = typeArray.map(type => {
        const found = this.mattersTypeOptions.find(opt => opt.dictValue == type);
        return found ? found.dictLabel : type;
      });
      return typeTexts.join('|');
    },

    // 获取督办类型标签
    getTaskTypeLabel(mattersType) {
      if (!mattersType) return '';
      const arr = mattersType.split(',');
      const typeTexts = arr.map(type => {
        const found = this.mattersTypeOptions.find(opt => opt.dictValue == type);
        return found ? found.dictLabel : type;
      });
      return typeTexts.join('|');
    },

    // 初始化字典数据
    initDictData() {
      // 获取数据字典
      this.dictTypes.forEach(type => {
        getDicts(type).then(res => {
          if (res.code === 200) {
            if (type === 'rant_classify') {
              this.categories = [{ dictValue: '', dictLabel: '全部' }].concat(res.data);
            } else if (type === 'rant_matters_type') {
              // 处理督办类型字典
              this.mattersTypeOptions = res.data;
            }
          }
        });
      });

      // 设置状态选项
      this.statuses = [
        { value: '', label: '全部' },
        { value: '0', label: '草稿' },
        { value: '1', label: '进行中' },
        { value: '2', label: '按时完成' },
        { value: '3', label: '延期完成' },
        { value: '4', label: '延期未完成' },
        { value: '5', label: '终止' },
        { value: '6', label: '审批中' },
        { value: '7', label: '驳回' }
      ];
    },

    // 获取状态显示文本
    getStatusText(status) {
      const statusMap = rantStatusOption.find(s => s.value === status);
      return statusMap ? statusMap.label : '未知';
    },

    getStatusClass(status) {
      return getStatusClass(status);
    },

    // 加载任务列表
    loadTaskList() {
      this.queryParams.pageNum = 1;
      this.isEnd = false;
      this.taskList = [];
      this.loadData();
    },

    // 统一的数据加载方法
    loadData() {
      if (this.loading) return Promise.resolve();

      this.loading = true;
      // 过滤掉空参数
      const filteredParams = this.filterEmptyParams(this.queryParams);

      return listMatters(filteredParams).then(res => {
        this.loading = false;
        if (res.code === 200) {
          this.total = res.total;
          const newList = res.rows || [];

          if (this.queryParams.pageNum === 1) {
            // 如果是第一页，则直接替换列表
            this.taskList = newList;
          } else {
            // 如果是加载更多，则拼接到现有列表
            this.taskList = this.taskList.concat(newList);
          }

          // 更新是否到底部的状态
          this.isEnd = this.taskList.length >= this.total || newList.length < this.queryParams.pageSize;

        } else {
          uni.showToast({
            icon: 'none',
            title: res.msg || '加载失败'
          });
        }
      }).catch(err => {
        this.loading = false;
        console.error('加载数据失败:', err);
        uni.showToast({
          icon: 'none',
          title: '网络错误，请稍后重试'
        });
      });
    },

    // 过滤空参数
    filterEmptyParams(params) {
      const filteredParams = {};

      Object.keys(params).forEach(key => {
        const value = params[key];

        // 检查各种类型的空值
        if (value !== undefined && value !== null && value !== '') {
          // 对于数组，只有当数组非空时才添加
          if (Array.isArray(value)) {
            if (value.length > 0) {
              filteredParams[key] = value;
            }
          } else {
            filteredParams[key] = value;
          }
        }
      });

      return filteredParams;
    },

    // 打开类型弹窗
    openCategoryPopup() {
      this.$refs.categoryPicker.show();
    },

    // 关闭类型弹窗
    closeCategoryPopup() {
      this.$refs.categoryPopup.close();
    },

    // 选择分类
    selectCategory(e) {
      const value = e.detail.value;
      const category = this.categories.find(item => item.dictValue === value);
      if (category) {
        this.selectedCategory = category.dictValue;
        this.queryParams.rantClassify = category.dictValue === '' ? '' : category.dictLabel;
        this.closeCategoryPopup();
        this.loadTaskList();
      }
    },

    // 打开状态弹窗
    openStatusPopup() {
      this.$refs.statusPicker.show();
    },

    // 关闭状态弹窗
    closeStatusPopup() {
      this.$refs.statusPicker.close();
    },

    // 选择状态
    selectStatus(e) {
      const value = e.detail.value;
      const status = this.statuses.find(item => item.value === value);
      if (status) {
        this.selectedStatus = status.value;
        this.queryParams.status = status.value;
        this.closeStatusPopup();
        this.loadTaskList();
      }
    },

    // 打开更多弹窗
    openMorePopup() {
      this.$refs.morePopup.open();
    },

    // 关闭更多弹窗
    closeMorePopup() {
      this.$refs.morePopup.close();
    },

    // 按日期排序
    sortByDate(order) {
      this.sortOrder = order;
      this.queryParams.orderByColumn = 'plan_time';
      this.queryParams.isAsc = order === 'asc' ? 'asc' : 'desc';
      this.closeMorePopup();
      this.loadTaskList();
    },

    // 筛选我负责的
    filterByResponsiblePerson() {
      this.filterType = this.filterType === 'myResponsibility' ? 'all' : 'myResponsibility';
      this.closeMorePopup();
      this.loadTaskList();
    },

    // 筛选我的督办
    filterByMyRant() {
      this.filterType = this.filterType === 'myRant' ? 'all' : 'myRant';
      this.closeMorePopup();
      this.loadTaskList();
    },

    // 跳转到搜索页面
    goToSearch() {
      uni.navigateTo({
        url: '/pages/matters/search'
      });
    },

    // 跳转到详情页面
    navigateToDetail(id) {
      uni.navigateTo({
        url: `/pages/look/detail?id=${id}&type=matters&showRanterName=true`
      });
    },

    // 跳转到创建页面
    navigateToCreate() {
      uni.navigateTo({
        url: '/pages/matters/create'
      });
    },

    // 编辑督办事项
    editRantItem(task) {
      if (task.status == 1 || task.status == 4) { // 编辑时，使用传阅接口/submitIn
        uni.navigateTo({
          url: `/pages/matters/create?id=${task.id}&type=edit&useSubmitIn=true`
        });
        return;
      }
      uni.navigateTo({
        url: `/pages/matters/create?id=${task.id}&type=edit`
      });
    },

    // 确认删除督办事项
    confirmDelete(task) {
      this.rantToDelete = task;
      this.$refs.deleteConfirmPopup.open();
    },

    // 处理删除确认
    handleDeleteConfirm() {
      if (!this.rantToDelete) return;

      uni.showLoading({
        title: '删除中...'
      });

      delMatters(this.rantToDelete.id).then(res => {
        uni.hideLoading();

        if (res.code === 200) {
          uni.showToast({
            icon: 'success',
            title: '删除成功'
          });

          // 更新列表
          this.taskList = this.taskList.filter(item => item.id !== this.rantToDelete.id);
        } else {
          uni.showToast({
            icon: 'none',
            title: res.msg || '删除失败'
          });
        }

        this.closeDeleteConfirm();
      }).catch(err => {
        uni.hideLoading();
        console.error('删除失败:', err);
        uni.showToast({
          icon: 'none',
          title: '网络错误，请稍后重试'
        });
        this.closeDeleteConfirm();
      });
    },

    // 关闭删除确认弹窗
    closeDeleteConfirm() {
      this.rantToDelete = null;
      this.$refs.deleteConfirmPopup.close();
    },

    // 确认终止督办事项
    endRantItem(task) {
      if (task.status == 5) {
        // 启用
        this.popupTitle = '确认启用';
        this.popupContent = '确认要启用此督办事项吗？';
        this.changeStatus = 1;
      } else {
        // 终止
        this.popupTitle = '确认终止';
        this.popupContent = '确认要终止此督办事项吗？';
        this.changeStatus = 5;
      }
      this.itemToEnd = task;
      this.$refs.endConfirmPopup.open();
    },

    // 处理终止确认
    handleEndConfirm() {
      if (!this.itemToEnd) return;

      uni.showLoading({
        title: '处理中...'
      });

      changeRantMattersStatus(this.itemToEnd.id, this.changeStatus).then(res => {
        uni.hideLoading();

        if (res.code === 200) {
          uni.showToast({
            icon: 'success',
            title: '已' + (this.changeStatus == 1 ? '启用' : '终止')
          });

          // 更新状态
          const index = this.taskList.findIndex(item => item.id === this.itemToEnd.id);
          if (index !== -1) {
            this.taskList[index].status = this.changeStatus;
            this.loadTaskList();
          }
        } else {
          uni.showToast({
            icon: 'none',
            title: res.msg || '操作失败'
          });
        }

        this.closeEndConfirm();

      }).catch(err => {
        uni.hideLoading();
        console.error('终止失败:', err);
        uni.showToast({
          icon: 'none',
          title: '网络错误，请稍后重试'
        });
        this.closeEndConfirm();
      });
    },

    // 关闭终止确认弹窗
    closeEndConfirm() {
      this.itemToEnd = null;
      this.$refs.endConfirmPopup.close();
    },

    // 重置查询参数
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        rantContent: '',
        rantClassify: '',
        status: '',
        mattersType: [],
        responsiblePerson: '',
        ranter: ''
      };
      this.selectedCategory = '';
      this.selectedStatus = '';
      this.sortOrder = '';
      this.filterType = 'all';
      this.loadTaskList();
    },

    // 处理搜索输入（带防抖）
    handleSearchInput() {
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout);
      }

      this.searchTimeout = setTimeout(() => {
        this.queryParams.pageNum = 1;
        this.isEnd = false;
        this.taskList = [];
        this.loadData();
      }, 500); // 500ms防抖
    },

    // 处理搜索
    handleSearch() {
      this.queryParams.pageNum = 1;
      this.isEnd = false;
      this.taskList = [];
      this.loadData();
    },

    // 清除搜索
    clearSearch() {
      this.queryParams.rantContent = '';
      this.queryParams.pageNum = 1;
      this.isEnd = false;
      this.taskList = [];
      this.loadData();
    },

    getStatusLabel(value) {
      if (!value && value !== 0) return '状态';
      const status = rantStatusOption.find(s => s.value === Number(value));
      return status ? status.label : '状态';
    },

    getSelectedCategoryLabel() {
      if (!this.selectedCategory) {
        return '类型';
      }
      const category = this.categories.find(item => item.dictValue === this.selectedCategory);
      return category ? category.dictLabel : '类型';
    },

    // 触摸开始
    touchStart(e) {
      this.isTouching = true;
    },

    // 触摸移动
    touchMove(e) {
      if (!this.isTouching) return;

      const touch = e.touches[0];
      const windowWidth = uni.getSystemInfoSync().windowWidth;
      const windowHeight = uni.getSystemInfoSync().windowHeight;

      // 计算新位置，确保按钮不会超出屏幕
      let newX = touch.clientX;
      let newY = touch.clientY;

      // 确保按钮不会超出屏幕边界
      if (newX < 50) newX = 50;
      if (newX > windowWidth - 50) newX = windowWidth - 50;
      if (newY < 50) newY = 50;
      if (newY > windowHeight - 50) newY = windowHeight - 50;

      // 更新按钮位置
      this.btnPosition.x = newX;
      this.btnPosition.y = newY;

      // 阻止默认行为和冒泡
      e.preventDefault();
      e.stopPropagation();
    },

    // 处理创建按钮点击事件
    handleCreateBtn() {
      // 如果是移动状态，不触发导航
      /* if (this.isTouching) {
        this.isTouching = false;
        return;
      } */

      // 正常导航
      this.navigateToCreate();
    }
  }
}
</script>

<style scoped lang="scss">
.task-container {
  height: 100vh;
  background-color: #D6E1F1;
  display: flex;
  flex-direction: column;
}

.filter-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  background-color: #D6E1F1;
  padding: 30rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.scroll-container {
  flex: 1;
  margin-top: 140rpx;
  /* 为固定的filter-area预留空间 */
  height: calc(100vh - 140rpx);
}

.task-list {
  padding: 0 24rpx 160rpx;
}

.task-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.task-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

/* .task-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  flex: 1;
} */

.task-date {
  font-size: 28rpx;
  color: #999;
}

.task-subheader {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.person-type {
  display: flex;
  align-items: center;
}

.task-person {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
}

.task-type {
  font-size: 28rpx;
  margin-right: 20rpx;
  // padding: 2px 6px;
  // background-color: #e8f0fe;
  // color: #1890ff;
  border-radius: 8rpx;
}

.task-content {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.task-responsibility {
  display: flex;
  flex-direction: column;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.item-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20rpx;
}

.action-btn {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  margin-left: 20rpx;
}

.edit-btn {
  background-color: #4080FF;
  color: white;
}

.delete-btn {
  background-color: #FF4D4F;
  color: white;
}

.end-btn {
  background-color: #fa8c16;
  color: white;
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
}

.create-btn {
  position: fixed;
  bottom: 60rpx;
  right: 60rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #1890ff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.4);
  z-index: 999;
  transform: translate3d(v-bind('btnPosition.x - 25 + "px"'), v-bind('btnPosition.y - 25 + "px"'), 0);
}

.plus-icon {
  font-size: 48rpx;
  color: #fff;
  //margin-bottom: 5px;
}

.popup-content {
  background-color: #fff;
  border-radius: 16rpx 16rpx 0 0;
  padding: 30rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #eee;
}

.popup-list {
  padding: 20rpx 0;
}

.popup-footer {
  margin-top: 30rpx;
  display: flex;
  justify-content: space-between;
}

.popup-btn {
  width: 30%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #F5F5F5;
  color: #333333;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  padding: 24rpx 10rpx;
}

.checkbox-item .selected {
  color: #4080FF;
  font-weight: bold;
}

.popup-btn-cancel {
  background-color: #F5F5F5;
}

.popup-btn-reset {
  background-color: #F5F5F5;
}

.popup-btn-confirm {
  background-color: #4080FF;
  color: #FFFFFF;
}

.radio-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
  font-size: 28rpx;
  color: #333333;
}

.popup-footer {
  margin-top: 30rpx;
  display: flex;
  justify-content: space-between;
}

.popup-btn {
  width: 30%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #F5F5F5;
  color: #333333;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.popup-item {
  padding: 24rpx 10rpx;
  font-size: 28rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.radio-item {
  display: flex;
  align-items: center;
}

.option-text {
  margin-left: 16rpx;
}

.popup-item .selected {
  color: #4080FF;
  font-weight: bold;
}

/* .popup-footer {
  padding: 15px 0 10px;
  display: flex;
  justify-content: center;
} */

/* .popup-btn {
  width: 80%;
  text-align: center;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 10px 0;
  font-size: 14px;
  color: #333;
} */
.popup-btn-cancel {
  background-color: #F5F5F5;
}

.popup-btn-reset {
  background-color: #F5F5F5;
}

.popup-btn-confirm {
  background-color: #4080FF;
  color: #FFFFFF;
}

.loading-more,
.no-more,
.empty-list {
  text-align: center;
  padding: 30rpx 0;
  color: #999999;
  font-size: 24rpx;
}
</style>
