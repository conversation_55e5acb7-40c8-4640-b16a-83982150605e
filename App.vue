<template>
  <view>
    <uni-popup ref="popup" type="bottom" ></uni-popup>
  </view>
</template>
<script>
import {handleLogin} from "@/utils/utils.js";
	export default {
		onLaunch: function() {
			console.log('App Launch')
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style>
	/*每个页面公共css */
	page {
		background-color: #f5f5f5;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
		font-size: 14px;
		color: #333;
		min-height: 100%;
	}

	view, text {
		box-sizing: border-box;
	}

	.container {
		padding: 20rpx;
	}
</style>
