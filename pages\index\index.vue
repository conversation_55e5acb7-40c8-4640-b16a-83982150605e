<template>
  <view class="container">
    <!-- 用户信息区域 -->
    <view class="user-info">
      <!-- <image class="avatar" :src="userInfo.avatar" mode="aspectFill"></image> -->
      <div class="avatar">
        <image class="avatar" src="@/static/images/avatar.png" mode="aspectFill"></image>
      </div>
      <view class="user-detail">
        <text class="username">{{ userInfo.nickName }}</text>
        <view class="department-container">
          <text class="department">{{ !!userInfo.dept ? userInfo.dept.deptName : '--' }}</text>
          <text class="relogin" @tap="relogin">重新登录</text>
        </view>
        
      </view>
    </view>

    <!-- 功能卡片区域 -->
    <view class="function-cards">
      <view class="card-row">
        <view class="function-card" @tap="navigateTo('/pages/look/index')" v-if="permissions.includes('rant:matters:lookList')">
          <image class="card-icon" src="/static/images/matters-check.png"></image>
          <text class="card-text">督办查看</text>
        </view>
        <view class="function-card" @tap="navigateTo('/pages/myRant/index')" v-if="permissions.includes('rant:matters:myRantList')">
          <image class="card-icon" src="/static/images/rant.png"></image>
          <text class="card-text">我要吐槽</text>
        </view>
     <!--  </view>
      <view class="card-row"> -->
        <view class="function-card" @tap="navigateTo('/pages/myTask/index')" v-if="permissions.includes('rant:matters:myTaskList')">
          <image class="card-icon" src="/static/images/task.png"></image>
          <text class="card-text">我的任务</text>
        </view>
        
        <view class="function-card" @tap="navigateTo('/pages/myFeedback/index')" v-if="permissions.includes('rant:record:list')">
          <image class="card-icon" src="/static/images/feedback.png"></image>
          <text class="card-text">我的反馈</text>
        </view>
     <!--  </view>
      <view class="card-row"> -->
        <view class="function-card" @tap="navigateTo('/pages/matters/index')" v-if="permissions.includes('rant:matters:list')">
          <image class="card-icon" src="/static/images/matters-mg.png"></image>
          <text class="card-text">督办管理</text>
        </view>
        <view class="function-card" style="visibility: hidden;">
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {handleLogin} from '@/utils/utils.js';
import { getMatters, listMatters, addMatters, updateMatters, delMatters, myTaskList, myRantList } from '@/api/rant/matters'
import store from '@/store';
import { mapState } from 'vuex';
import { getInfo } from '@/api/login'
export default {
  data() {
    return {
    }
  },
  computed: {
    ...mapState(['userInfo', 'permissions'])
  },
  onShow() {
    if(store.state.access_token){
      this.getAuthorityInfo()
    }
  },
  onLoad() {
    // 加载用户信息
    // if(!store.state.access_token) {
      this.getUserInfo(false, false)
    // }
  },
  methods: {
    getAuthorityInfo() {
      getInfo().then(res => {
        console.log('getAuthorityInfo', res);
        store.commit('setPermissions', res.permissions);
      })
    },
    getUserInfo(relaunch=true, showLoading=true) {
      handleLogin(relaunch, showLoading).then(res => {
        console.log('handleLogin', res);
        this.getAuthorityInfo();
      })
        .catch(err => {
          console.error('Login failed:', err);
          uni.showToast({
            title: '登录失败',
            icon: 'none'
          });
        })
    },
    relogin(){
      this.getUserInfo(false, true)
    },
    navigateTo(url) {
      console.log('Navigating to:', url);
      uni.navigateTo({
        url: url,
        fail: (err) => {
          console.error('Navigation failed:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  background-color: #D6E1F1;
  min-height: 100vh;
}

.user-info {
  display: flex;
  align-items: center;
  margin: 30rpx 0;
}

.avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  // border: 2rpx solid #FFFFFF;
  margin-right: 24rpx;
  border-radius: 50%;
}

.user-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.username {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.department-container{
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.department {
  font-size: 28rpx;
  color: #666;
  min-width: 0;
  flex-grow: 1;
  word-break: break-all;
}
.relogin{
  background-color: #4080FF;
  color: white;
  padding: 6rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  margin-left: 16rpx;
  width: -webkit-fit-content;
  width: fit-content;
  word-break: keep-all;
  height: 45rpx;
}
.function-cards {
  margin-top: 40rpx;
}

.card-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
  gap: 24rpx;
  flex-wrap: wrap;
}

.function-card {
  // flex: 1;
  background-color: #EBF0F8;
  border-radius: 10rpx;
  padding: 32rpx 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  width: calc(50vw - 34rpx);
}

.card-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.card-text {
  font-size: 32rpx;
  font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
  font-weight: 500;
}
</style>
