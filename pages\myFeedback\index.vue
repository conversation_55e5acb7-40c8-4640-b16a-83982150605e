<template>
  <view class="rant-list-container">
    <view class="custom-tabs" v-if="shouldShowTabs">
      <view :class="['tab-item', { active: shouldShowTabs && activeTab === '1' }]" @tap="handleTabClick('1')">我的</view>
      <view :class="['tab-item', { active: shouldShowTabs && activeTab === '2' }]" @tap="handleTabClick('2')">所有</view>
    </view>

    <!-- 筛选区域 -->
    <view :class="['filter-area', { 'has-tabs': shouldShowTabs }]">
      <view class="filter-item" @tap="openTypePopup">
        <text>{{ getSelectedTypesLabel() || '类型' }}</text>
        <uni-icons type="bottom" :size="12"></uni-icons>
      </view>
      <view class="filter-item" @tap="openStatusPopup">
        <text>{{ getStatusLabel(queryParams.mattersStatus) || '状态' }}</text>
        <uni-icons type="bottom" :size="12"></uni-icons>
      </view>
      <view class="filter-item" @tap="openFeedbackStatusPopup">
        <text>{{ getFeedbackStatusLabel(queryParams.status) || '反馈状态' }}</text>
        <uni-icons type="bottom" :size="12"></uni-icons>
      </view>
      <view class="search-box">
        <!-- <text class="icon-search">🔍</text> -->
        <uni-icons class="icon-search" type="search" size="20"></uni-icons>
        <input class="search-input" type="text" v-model="queryParams.rantContent" placeholder="搜索" confirm-type="search"
          @input="handleSearchInput" @confirm="handleSearch" />
        <text class="clear-icon" v-if="queryParams.rantContent" @tap="clearSearch">×</text>
      </view>
    </view>

    <!-- 列表内容 -->
    <scroll-view scroll-y :class="['rant-scroll-view', { 'has-tabs': shouldShowTabs }]"
      @scrolltolower="loadMoreData" refresher-enabled
      :refresher-triggered="isRefreshing" @refresherrefresh="onRefresh">
      <!-- <view class="simple-list"> -->
        <view v-for="task in recordList" :key="task.id" class="task-card" @tap="navigateToDetail(task)">
          <view class="task-header">
            <!-- 督办类型 -->
            <text class="task-title">{{ getTaskTypeLabel(task.mattersType) || task.title }}</text>
            <!-- 事项状态 -->
            <view :class="['task-status', getStatusClass(task.mattersStatus)]">{{ getStatusText(task.mattersStatus) }}
            </view>
          </view>
          <view class="task-subheader">
            <view class="person-type">
              <!-- 来源 -->
              <!-- <text class="task-person">{{task.ranterName}}</text> -->
              <!-- 责任人 -->
              <text class="task-person">{{ task.responsiblePersonName || '--' }}</text>
              <!-- <text class="task-person">{{task.responsiblePersonName || task.person}}</text> -->
              <!-- <text class="task-type">{{task.rantClassify || task.type}}</text> -->
            </view>
            <!-- 分类 -->
            <text class="task-type">{{ task.rantClassify }}</text>
          </view>
          <!-- 督办内容 -->
          <view class="task-content">
            <text>事项内容：</text>
            <text>{{ task.rantContent }}</text>
          </view>
          <view class="task-content">
            <text>反馈来源：</text>
            <text>{{ filterFeedbackSource(task.feedbackSource) || "--" }}</text>
          </view>
          <view class="task-content">
            <text>本次进展：</text>
            <text>{{ task.actualProgress || "--" }}</text>
          </view>
          <!-- 如果督办事项状态为按期完成或者延期完成，就显示第四行 左边是结项时间标题，右边是结项日期 -->
          <view v-if="(task.status == 2 || task.status == 3) && task.closingTime" class="task-content cu-flex-between">
            <text>结项时间：</text>
            <text>{{ task.closingTime || "--" }}</text>
          </view>
          <view class="task-content">
            <text>反馈状态：</text>
            <view :class="['status-tag', getFeedbackStatusClass(task.status)]">{{ filterFeedbackStatus(task.status) }}
            </view>
          </view>
          <view class="item-actions">
            <view v-if="task.status == 0 || task.status == 3" class="action-btn edit-btn" @tap.stop="editRecord(task)">
              修改</view>
            <view v-if="task.status == 0 || task.status == 3" class="action-btn delete-btn"
              @tap.stop="confirmDelete(task)">删除</view>
            <view v-if="task.approveFlag == 1" class="action-btn edit-btn" @tap.stop="approvalRecord(task)">审批详情</view>
          </view>
        </view>

        <!-- 加载更多提示 -->
        <view class="loading-more" v-if="isLoading">
          <view class="loading-icon"></view>
          <text>加载中...</text>
        </view>
        <view class="no-more" v-if="!hasMore && recordList.length > 0">
          <text>没有更多数据了</text>
        </view>
        <view class="empty-list" v-if="!isLoading && recordList.length === 0">
          <text>暂无数据</text>
        </view>
      <!-- </view> -->
    </scroll-view>

    <!-- 创建按钮 -->
    <view class="create-btn" @tap="navigateToCreate" v-if="activeTab == '1'">
      <!--      <text class="plus-icon">+</text>-->
      <uni-icons type="plusempty" size="30" :color="'#fff'"></uni-icons>
    </view>
    <!-- 分类选择组件 -->
    <category-picker ref="typePopup" title="选择类型" dictType="rant_matters_type" :multiple="true"
      @confirm="applyTypeSelection" />
    <!-- 状态选择组件 -->
    <status-picker ref="statusPopup" title="选择状态" @confirm="applyStatusSelection" :multiple="true"/>
    <feedback-status-picker ref="feedbackStatusPopup" title="选择反馈状态" @confirm="applyFeedbackStatusSelection" :multiple="true"/>
    <!-- 删除确认弹出层 -->
    <uni-popup ref="deleteConfirmPopup" type="dialog">
      <uni-popup-dialog type="warn" title="确认删除" content="确定要删除此反馈记录吗？此操作不可恢复。" :before-close="false" :show-close="true"
        cancelText="取消" confirmText="确认删除" @confirm="handleDeleteConfirm"
        @close="closeDeleteConfirm"></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
import { listRecord, delRecord } from '@/api/rant/record';
import { getDicts } from '@/api/common';
import { rantStatusOption, feedbackStatusOption } from '@/constant/index'
import CategoryPicker from '@/components/CategoryPicker/index.vue'
import StatusPicker from '@/components/StatusPicker/index.vue'
import FeedbackStatusPicker from '@/components/FeedbackStatusPicker/index.vue'
import mixin from '@/mixins/mixin';
import store from '@/store';
import { mapState } from 'vuex';
export default {
  mixins: [mixin],
  components: {
    CategoryPicker,
    StatusPicker,
    FeedbackStatusPicker
  },
  data() {
    return {
      recordList: [],
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      isLoading: false,
      isRefreshing: false,
      hasMore: true,
      scrollViewHeight: 350, // 默认高度，会在onReady中重新计算
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mattersType: null,
        rantClassify: null,
        mattersStatus: null,
        feedbackSource: null,
        status: null,
        rantContent: null,
        isPerson: null
      },
      searchText: '',
      selectedCategory: '全部',
      selectedStatus: '全部',
      selectedSource: '全部',
      selectedMattersType: '全部',
      categoryOptions: [{ value: null, label: '全部' }],
      statusOptions: [
        { value: null, label: '全部' },
        { value: 0, label: '待审核' },
        { value: 1, label: '通过' },
        { value: 2, label: '终止' },
        { value: 3, label: '驳回' }
      ],
      statuses: [
        { value: '', label: '全部' },
        { value: '0', label: '草稿' },
        { value: '1', label: '进行中' },
        { value: '2', label: '按时完成' },
        { value: '3', label: '延期完成' },
        { value: '4', label: '延期未完成' },
        { value: '5', label: '终止' },
        { value: '6', label: '审批中' },
        { value: '7', label: '驳回' }
      ],
      sourceOptions: [{ value: null, label: '全部' }],
      mattersTypeOptions: [{ value: null, label: '全部' }],
      recordToDelete: null,
      mattersStatusMap: {
        0: '草稿',
        1: '进行中',
        2: '按时完成',
        3: '延期完成',
        4: '延期未完成',
        5: '终止',
        6: '审批中',
        7: '驳回'
      },
      selectedTypes: [],
      selectedCategory: '全部',
      selectedStatus: '全部',
      types: [],
      categories: [],
      sourceDictOptions: [],
      activeTab: '1'
    }
  },
  computed: {
    feedbackStatusOption() {
      return feedbackStatusOption
    },
    // 计算是否显示tabs
    shouldShowTabs() {
      return this.permissions.includes('rant:record:AllList')
    },
    ...mapState(['userInfo', 'permissions'])
  },
  onLoad() {
    // 获取字典数据
    this.fetchDicts();
    this.fetchTypes();
  },
  // onReady() {
  //    // 延迟执行精确计算确保DOM完全渲染
  //    this.$nextTick(() => {
  //     setTimeout(() => {
  //       this.calculateScrollViewHeight();
  //     }, 300);
  //   });
  // },
  async onShow() {
    // 调试permissions
    console.log('permissions:', this.permissions);
    console.log('includes rant:record:AllList:', this.permissions.includes('rant:record:AllList'));

    // 如果没有字典数据，则获取
    if (!this.sourceDictOptions || this.sourceDictOptions.length === 0) {
      await this.fetchFeedbackSource();
    }
    this.queryParams.isPerson = this.activeTab;
    // 当页面显示时，如果列表为空则加载第一页数据
    this.resetAndLoad();
  },
  mounted() {
    // console.log('mounted');
    // // 计算滚动区域高度
    //  // 延迟执行精确计算确保DOM完全渲染
    //  this.$nextTick(() => {
    //   setTimeout(() => {
    //     this.calculateScrollViewHeight();
    //   }, 300);
    // });
  },
  methods: {
    approvalRecord(task) {
      uni.navigateTo({
        url: `/pages/myFeedback/approval?id=${task.id}&type=approval`
      });
    },
    handleTabClick(tab) {
      this.activeTab = tab;
      this.queryParams.isPerson = tab;
      this.resetAndLoad();
    },
    navigateToDetail(task) {
      /*  uni.navigateTo({
         url: `/pages/look/detail?id=${taskId}&type=look`
       }); */
      uni.navigateTo({
        url: `/pages/myFeedback/detail?id=${task.id}&type=look&feedbackSource=${task.feedbackSource}&actualProgress=${task.actualProgress}&status=${task.status}`
      });
    },
    filterFeedbackSource(value) {
      // 如果值为空或字典未加载，返回默认值
      if (!value || !this.sourceDictOptions || this.sourceDictOptions.length === 0) return '--';

      // 将value转为字符串进行比较，因为接口可能返回数字或字符串
      const strValue = String(value);
      const option = this.sourceDictOptions.find(item => String(item.dictValue) === strValue);
      return option ? option.dictLabel : value;
    },
    async fetchFeedbackSource() {
      try {
        // 如果已经有数据，就不再请求
        if (this.sourceDictOptions && this.sourceDictOptions.length > 0) {
          return { code: 200, data: this.sourceDictOptions };
        }

        const res = await getDicts('rant_feedback_source');
        if (res.code === 200 && res.data) {
          // 保存完整的字典数据对象到sourceDictOptions
          this.sourceDictOptions = res.data;
          console.log('反馈来源字典数据:', this.sourceDictOptions);
        }
        return res;
      } catch (error) {
        console.error('获取反馈来源字典失败:', error);
        uni.showToast({
          title: '获取字典数据失败',
          icon: 'none'
        });
        return { code: 500, msg: error.message };
      }
    },
    filterFeedbackStatus(status) {
      return this.feedbackStatusOption.find(item => item.value === status)?.label || status;
    },
    handleSearch() {
      this.resetAndLoad();
    },

    // 清除搜索
    clearSearch() {
      this.queryParams.rantContent = '';
      this.resetAndLoad();
    },
    handleSearchInput() {
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout);
      }

      this.searchTimeout = setTimeout(() => {
        this.resetAndLoad();
      }, 500); // 500ms防抖
    },
    toggleStatusSelection(value) {
      // Convert the selected status value to number if it's numeric
      const numValue = value === '' ? '' : Number(value);

      if (this.queryParams.status === numValue) {
        this.queryParams.status = '';
      } else {
        this.queryParams.status = numValue;
      }
    },
    toggleTypeSelection(value) {
      if (this.selectedTypes.includes(value)) {
        this.selectedTypes = this.selectedTypes.filter(v => v !== value);
      } else {
        this.selectedTypes.push(value);
      }
      console.log('selectedTypes:', this.selectedTypes);
    },
    resetTypeSelection() {
      this.selectedTypes = [];
    },
    applyTypeSelection(selectedTypes) {
      console.log('selectedTypes:', selectedTypes);
      this.queryParams.mattersType = selectedTypes;
      this.resetAndLoad();
    },
    // 获取类型选项
    async fetchTypes() {
      try {
        // 使用getDicts API获取字典数据
        const res = await getDicts('rant_matters_type');
        if (res.code === 200 && res.data) {
          // 添加"全部"选项
          this.types = [{ dictValue: '', dictLabel: '全部' }, ...res.data.map(item => ({
            dictValue: item.dictValue,
            dictLabel: item.dictLabel
          }))];
        }
      } catch (error) {
        console.error('获取类型选项失败:', error);
        uni.showToast({
          icon: 'none',
          title: '获取类型选项失败'
        });
      }
    },
    openTypePopup() {
      this.$refs.typePopup.show();
    },
    closeTypePopup() {
      this.$refs.typePopup.close();
    },
    openStatusPopup() {
      this.$refs.statusPopup.show();
    },

    closeStatusPopup() {
      this.$refs.statusPopup.close();
    },
    getStatusLabel(value) {
      if (!value && value !== 0) return '状态';
      const status = this.statuses.find(s => s.value === value);
      return status ? status.label : '状态';
    },
    getSelectedTypesLabel() {
      if (!this.selectedTypes || this.selectedTypes.length === 0) {
        return '类型';
      }

      const typeLabels = this.selectedTypes.map(typeValue => {
        const type = this.types.find(t => t.dictValue === typeValue);
        return type ? type.dictLabel : '';
      }).filter(label => label);

      if (typeLabels.length === 0) {
        return '类型';
      }

      if (typeLabels.length > 1) {
        return `已选${typeLabels.length}项`;
      }

      return typeLabels[0];
    },

    getTaskTypeLabel(value) {
      if (!value) return '';

      // If value is a comma-separated string (from API), split it into an array
      const typeValues = typeof value === 'string' ? value.split(',') :
        Array.isArray(value) ? value : [value];

      // Map each value to its label
      const typeLabels = typeValues.map(val => {
        const type = this.types.find(t => t.dictValue === val);
        return type ? type.dictLabel : '';
      }).filter(label => label); // Remove empty labels

      return typeLabels.join('|');
    },
    // 获取字典数据
    async fetchDicts() {
      try {
        // 获取分类字典
        const rantClassifyRes = await getDicts('rant_classify');
        if (rantClassifyRes.code === 200 && rantClassifyRes.data) {
          this.categoryOptions = [{ value: null, label: '全部' }, ...rantClassifyRes.data.map(item => ({
            value: item.dictValue,
            label: item.dictLabel
          }))];
        }

        // 获取反馈来源字典
        const feedbackSourceRes = await getDicts('rant_feedback_source');
        if (feedbackSourceRes.code === 200 && feedbackSourceRes.data) {
          // 转换为value/label格式用于筛选器
          this.sourceOptions = [{ value: null, label: '全部' }, ...feedbackSourceRes.data.map(item => ({
            value: item.dictValue,
            label: item.dictLabel
          }))];

          // 同时保存原始数据用于展示
          this.sourceDictOptions = feedbackSourceRes.data;
        }

        // 获取类型字典
        const mattersTypeRes = await getDicts('rant_matters_type');
        if (mattersTypeRes.code === 200 && mattersTypeRes.data) {
          this.mattersTypeOptions = [{ value: null, label: '全部' }, ...mattersTypeRes.data.map(item => ({
            value: item.dictValue,
            label: item.dictLabel
          }))];
        }
      } catch (error) {
        console.error('获取字典数据失败:', error);
      }
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 获取类型文本
    getMattersTypeText(types) {
      if (!types) return '';
      const typeArray = types.split(',');
      const typeTexts = typeArray.map(type => {
        const found = this.mattersTypeOptions.find(opt => opt.dictValue === type);
        return found ? found.dictLabel : type;
      });
      return typeTexts.join('|');
    },

    // 获取事项状态文本
    getMattersStatusText(status) {
      return this.mattersStatusMap[status] || status;
    },

    // 获取事项状态样式
    getMattersStatusClass(status) {
      const statusValue = Number(status);
      switch (statusValue) {
        case 4: // 延期未完成
          return 'status-delayed-unfinished';
        case 3: // 延期完成
          return 'status-delayed-finished';
        case 1: // 进行中
          return 'status-in-progress';
        case 2: // 按时完成
          return 'status-on-time';
        case 5: // 终止
          return 'status-ended';
        default:
          return '';
      }
    },

    // 获取反馈状态文本
    getStatusText(status) {
      const statusValue = Number(status);
      const statusMap = {
        0: '草稿',
        1: '进行中',
        2: '按时完成',
        3: '延期完成',
        4: '延期未完成',
        5: '终止',
        6: '审批中',
        7: '驳回'
      }
      return statusMap[statusValue] || statusValue;
    },

    calculateScrollViewHeight() {
      console.log('calculateScrollViewHeight');
      // 获取系统信息
      const systemInfo = uni.getSystemInfoSync();

      // 获取顶部筛选区域的高度
      const query = uni.createSelectorQuery().in(this);
      query.select('.filter-area').boundingClientRect(data => {
        if (data) {
          // 计算scrollView的高度 = 屏幕高度 - 筛选区域高度 - 底部安全区域 - 额外边距
          const filterHeight = data.height;
          const extraMargin = 10; // 底部的额外边距
          this.scrollViewHeight = systemInfo.windowHeight - filterHeight - extraMargin;
          console.log('scrollViewHeight:', this.scrollViewHeight);
        }
      }).exec();
    },

    resetAndLoad() {
      // 重置分页参数并加载第一页
      this.queryParams.pageNum = 1;
      this.hasMore = true;
      this.recordList = [];
      this.loadData();
    },

    async loadData() {
      if (this.isLoading || !this.hasMore) return;

      this.isLoading = true;

      try {
        // 创建查询参数，过滤掉空值
        const params = { ...this.queryParams };

        // 只添加非空的查询参数
        Object.keys(params).forEach(key => {
          if (params[key] === null ||
            params[key] === undefined ||
            params[key] === '' ||
            (Array.isArray(params[key]) && params[key].length === 0)) {
            delete params[key];
          }
        });

        // 添加搜索关键词
        if (this.searchText) {
          params.rantContent = this.searchText;
        }

        console.log('查询参数:', params);

        const response = await listRecord(params);

        if (response.code === 200) {
          const newRecords = response.rows || [];
          const total = response.total || 0;

          // 更新列表和分页信息
          if (this.queryParams.pageNum === 1) {
            this.recordList = newRecords;
          } else {
            this.recordList = [...this.recordList, ...newRecords];
          }

          this.pagination.total = total;

          // 判断是否还有更多数据
          this.hasMore = this.recordList.length < total;

          // 更新当前页码
          if (this.hasMore) {
            this.queryParams.pageNum += 1;
          }
        } else {
          uni.showToast({
            title: response.msg || '加载失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('Load data error:', error);
        uni.showToast({
          title: '网络异常，请稍后再试',
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
        this.isRefreshing = false;
      }
    },

    loadMoreData() {
      if (this.hasMore && !this.isLoading) {
        this.loadData();
      }
    },

    onRefresh() {
      this.isRefreshing = true;
      this.resetAndLoad();
    },

    onSearch() {
      this.resetAndLoad();
    },

    navigateToCreate() {
      uni.navigateTo({
        url: '/pages/myFeedback/create'
      });
    },

    /* getStatusClass(status) {
       const statusValue = Number(status);
       const statusObj = rantStatusOption.find(s => s.value === statusValue);
 
       if (!statusObj) return '';
 
       switch (statusValue) {
         case 0: // 草稿
           return 'status-draft';
         case 1: // 进行中
           return 'status-in-progress';
         case 2: // 按时完成
           return 'status-on-time';
         case 3: // 延期完成
           return 'status-delayed-finished';
         case 4: // 延期未完成
           return 'status-delayed-unfinished';
         case 5: // 终止
           return 'status-ended';
         case 6: // 审批中
           return 'status-approving';
         case 7: // 驳回
           return 'status-rejected';
         default:
           return '';
       }
     },*/


    // 分类筛选相关
    openCategoryPopup() {
      this.$refs.categoryPopup.open();
    },

    closeCategoryPopup() {
      this.$refs.categoryPopup.close();
    },

    selectCategory(item) {
      this.selectedCategory = item.label;
      if (item.label === '全部') {
        this.queryParams.rantClassify = null;
      } else {
        this.queryParams.rantClassify = item.label;
      }
      this.closeCategoryPopup();
      this.resetAndLoad();
    },

    // 状态筛选相关
    openStatusPopup() {
      this.$refs.statusPopup.show();
    },

    closeStatusPopup() {
      this.$refs.statusPopup.close();
    },

    openFeedbackStatusPopup() {
      this.$refs.feedbackStatusPopup.show();
    },

    closeFeedbackStatusPopup() {
      this.$refs.feedbackStatusPopup.close();
    },
    getFeedbackStatusLabel(status) {
      if (status === null || status === undefined || status === '') return '反馈状态';
      const statusValue = Number(status);
      const statusObj = feedbackStatusOption.find(s => s.value === statusValue);
      return statusObj ? statusObj.label : '反馈状态';
    },
    applyFeedbackStatusSelection(status) {
      this.selectedFeedbackStatus = status.value;
      this.queryParams.status = status.value;
      this.resetAndLoad();
    },
    applyStatusSelection(status) {
      this.selectedStatus = status.value;
      this.queryParams.mattersStatus = status.value;
      this.resetAndLoad();
    },

    // 来源筛选相关
    openSourcePopup() {
      this.$refs.sourcePopup.open();
    },

    closeSourcePopup() {
      this.$refs.sourcePopup.close();
    },

    selectSource(item) {
      this.selectedSource = item.label;
      this.queryParams.feedbackSource = item.value;
      this.closeSourcePopup();
      this.resetAndLoad();
    },

    // 类型筛选相关
    openMattersTypePopup() {
      this.$refs.mattersTypePopup.open();
    },

    closeMattersTypePopup() {
      this.$refs.mattersTypePopup.close();
    },

    selectMattersType(item) {
      this.selectedMattersType = item.label;
      this.queryParams.mattersType = item.value;
      this.closeMattersTypePopup();
      this.resetAndLoad();
    },

    // 编辑功能
    editRecord(item) {
      uni.navigateTo({
        url: `/pages/myFeedback/create?id=${item.id}`
      });
    },

    // 删除功能
    confirmDelete(item) {
      this.recordToDelete = item;
      this.$refs.deleteConfirmPopup.open();
    },

    closeDeleteConfirm() {
      this.recordToDelete = null;
      this.$refs.deleteConfirmPopup.close();
    },

    async handleDeleteConfirm() {
      if (!this.recordToDelete) return;

      try {
        uni.showLoading({
          title: '删除中...'
        });

        const res = await delRecord(this.recordToDelete.id);
        if (res.code === 200) {
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          });
          // 从列表中移除被删除的项
          this.recordList = this.recordList.filter(item => item.id !== this.recordToDelete.id);
        } else {
          throw new Error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        uni.showToast({
          title: '删除失败',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
        this.closeDeleteConfirm();
      }
    },

    // 返回反馈状态对应的样式类
    getFeedbackStatusClass(status) {
      const statusValue = Number(status);

      switch (statusValue) {
        case 0: // 草稿
          return 'status-draft';
        case 1: // 审批中
          return 'status-pending';
        case 2: // 审批通过
          return 'status-approved';
        case 3: // 驳回
          return 'status-rejected';
        default:
          return '';
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.rant-list-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #D6E1F1;
 
}

.custom-tabs {
  padding: 0 24rpx;
  display: flex;
  // background-color: #fff;
  background-color: #D6E1F1;
  border-bottom: 1px solid #e0e0e0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  height: 100rpx;

  .tab-item {
    flex: 1;
    text-align: center;
    padding: 30rpx 0;
    font-weight: 400;
    font-size: 32rpx;
    color: #333333;
    position: relative;

    &.active {
      font-size: 32rpx;
      color: #1761FF;
      font-weight: bold;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100%;
        height: 4rpx;
        background-color: #007AFF;
      }
    }
  }
}

/* 当 custom-tabs 被隐藏时，确保伪元素也被隐藏 */
.custom-tabs[style*="display: none"] .tab-item.active::after,
.custom-tabs[style*="display:none"] .tab-item.active::after {
  display: none !important;
}

.filter-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  background-color: #D6E1F1;
  padding: 20rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.filter-area.has-tabs {
  top: 100rpx;
}

.filter-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

/* .filter-item {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFFFFF;
  border-radius: 32rpx;
  padding: 10rpx 30rpx;
  margin-right: 16rpx;
  font-size: 28rpx;
} */

.arrow-down {
  margin-left: 8rpx;
  font-size: 24rpx;
  color: #999;
}

/* .search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #F5F5F5;
  border-radius: 30rpx;
  padding: 0 20rpx;
  margin-left: 10rpx;
  height: 70rpx;
} */

.icon-search {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
}

.clear-icon {
  font-size: 32rpx;
  color: #999;
  padding: 0 10rpx;
}

.rant-scroll-view {
  // flex: 1;
  // width: 100%;
  // margin-top: 100rpx;
  /* 为固定的filter-area预留空间 */
  // height: calc(100vh - 232rpx);
  position: fixed;
  top: 100rpx;
  /* filter-area的高度 */
  left: 0;
  right: 0;
  bottom: 0;
}

.rant-scroll-view.has-tabs {
  // margin-top: 200rpx;
  // height: calc(100vh - 200rpx);
  top: 228rpx;
}

.simple-list {
  padding: 0;
  margin-top: 20rpx;
}

.simple-item {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.item-department {
  font-weight: 500;
  font-size: 28rpx;
  color: #333333;
}

.item-date {
  font-size: 26rpx;
  color: #999;
}

.item-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
}

.item-label {
  font-size: 26rpx;
  color: #666;
}

.item-category {
  font-size: 26rpx;
  color: #666;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  background-color: rgba(0, 0, 0, 0.05);
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.matters-type {
  color: #4080FF;
}

.rant-title {
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.item-status {
  font-size: 26rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

/*.status-in-progress {
  color: #4080FF;
  background-color: rgba(64, 128, 255, 0.1);
}

.status-delayed-unfinished {
  color: #FF5252;
  background-color: rgba(255, 82, 82, 0.1);
}

.status-delayed-finished {
  color: #FF9900;
  background-color: rgba(255, 153, 0, 0.1);
}

.status-on-time {
  color: #4CAF50;
  background-color: rgba(76, 175, 80, 0.1);
}

.status-ended {
  color: #9E9E9E;
  background-color: rgba(158, 158, 158, 0.1);
}

.status-pending {
  color: #FF9800;
  background-color: rgba(255, 152, 0, 0.1);
}

.status-approved {
  color: #4CAF50;
  background-color: rgba(76, 175, 80, 0.1);
}

.status-rejected {
  color: #F44336;
  background-color: rgba(244, 67, 54, 0.1);
}

.status-terminated {
  color: #9E9E9E;
  background-color: rgba(158, 158, 158, 0.1);
}*/

.item-content {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.4;
  margin-top: 10rpx;
  /* 文本超出三行显示省略号 */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #eee;
}

.action-btn {
  padding: 6rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  margin-left: 16rpx;
}

.edit-btn {
  background-color: #4080FF;
  color: white;
}

.delete-btn {
  background-color: #FF4D4F;
  color: white;
}

.loading-more,
.no-more,
.empty-list {
  text-align: center;
  padding: 120rpx 0;
  color: #999999;
  font-size: 24rpx;
}

.loading-icon {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #4080FF;
  border-radius: 50%;
  margin-right: 10rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.create-btn {
  position: fixed;
  right: 40rpx;
  bottom: 80rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #4080FF;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
  z-index: 999;
}

.plus-icon {
  color: #FFFFFF;
  font-size: 60rpx;
  font-weight: 300;
  display: inline-block;
  margin-bottom: 20rpx;
}

/* 弹出层样式 */
.popup-content {
  background-color: #FFFFFF;
  border-radius: 16rpx 16rpx 0 0;
  padding: 30rpx;
}

.popup-title {
  font-weight: 500;
  font-size: 30rpx;
  color: #333333;
  text-align: center;
  margin-bottom: 30rpx;
}

.popup-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.popup-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
  font-size: 28rpx;
  color: #333333;
}

.popup-item .selected {
  color: #4080FF;
  font-weight: 500;
}

.popup-footer {
  margin-top: 30rpx;
  display: flex;
  justify-content: space-between;
}

.popup-btn {
  width: 25%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #F5F5F5;
  color: #333333;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.popup-btn-cancel {
  background-color: #F5F5F5;
}

.popup-btn-reset {
  background-color: #F5F5F5;
}

.popup-btn-confirm {
  background-color: #4080FF;
  color: #FFFFFF;
}

.status-tag {
  display: inline-block;
  font-size: 26rpx;
  padding: 4rpx 16rpx;
  border-radius: 4rpx;
  text-align: center;
}

.status-draft {
  color: #666666;
  background-color: #F5F5F5;
}

.status-pending {
  color: #FF9800;
  background-color: rgba(255, 152, 0, 0.1);
}

.status-approved {
  color: #4CAF50;
  background-color: rgba(76, 175, 80, 0.1);
}

.status-rejected {
  color: #FFFFFF;
  background-color: #F44336;
}
</style>
