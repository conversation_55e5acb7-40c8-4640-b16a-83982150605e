# 文件下载功能使用指南

## 概述

本项目提供了完整的文件下载功能，支持微信小程序和企业微信环境。功能包括：
- 单个文件下载
- 批量文件下载  
- 图片预览和保存
- 文档下载和打开
- 智能文件类型识别

## 快速开始

### 方式1: 导入使用（推荐）

```javascript
// 导入需要的方法
import { downloadFile, previewFile, saveImageToAlbum } from '@/utils/downloadHelper'

export default {
  methods: {
    async handleDownload() {
      // 下载文件
      await downloadFile('https://example.com/file.pdf')
      
      // 预览图片
      await previewFile('https://example.com/image.jpg')
      
      // 保存图片到相册
      await saveImageToAlbum('https://example.com/photo.jpg')
    }
  }
}
```

### 方式2: 全局方法使用

```javascript
export default {
  methods: {
    async handleDownload() {
      // 使用全局方法
      await this.$downloadFile('https://example.com/file.pdf')
      await this.$previewFile('https://example.com/image.jpg')
      await this.$saveImageToAlbum('https://example.com/photo.jpg')
    }
  }
}
```

## API 详细说明

### downloadFile(url, options)

下载单个文件

**参数:**
- `url` (String): 文件下载地址
- `options` (Object): 配置选项

**配置选项:**
```javascript
{
  showLoading: true,        // 是否显示加载提示
  loadingText: '下载中...',  // 加载提示文字
  autoOpen: true,           // 是否自动打开文件
  saveToAlbum: false,       // 是否保存到相册（仅图片）
  fileName: '',             // 自定义文件名
  showSuccessToast: true,   // 是否显示成功提示
  showErrorToast: true,     // 是否显示错误提示
  timeout: 30000,           // 超时时间（毫秒）
  headers: {}               // 请求头
}
```

**使用示例:**
```javascript
// 基本用法
await downloadFile('https://example.com/file.pdf')

// 自定义配置
await downloadFile('https://example.com/file.pdf', {
  fileName: '重要文档.pdf',
  autoOpen: false,
  showSuccessToast: true
})
```

### previewFile(url, options)

预览文件（图片直接预览，文档下载后打开）

```javascript
// 预览图片
await previewFile('https://example.com/image.jpg')

// 预览文档
await previewFile('https://example.com/document.pdf', {
  loadingText: '加载文档中...'
})
```

### saveImageToAlbum(url, options)

保存图片到相册

```javascript
await saveImageToAlbum('https://example.com/photo.jpg', {
  loadingText: '保存中...'
})
```

### downloadFiles(urls, options)

批量下载文件

```javascript
const urls = [
  'https://example.com/file1.pdf',
  'https://example.com/file2.jpg',
  'https://example.com/file3.docx'
]

const result = await downloadFiles(urls, {
  concurrent: 3,      // 并发下载数量
  showProgress: true  // 显示进度
})

console.log('下载结果:', result)
// {
//   success: [...],     // 成功的文件
//   errors: [...],      // 失败的文件
//   total: 3,           // 总数
//   successCount: 2,    // 成功数量
//   errorCount: 1       // 失败数量
// }
```

### quickPreview(url)

快速预览（自动判断文件类型）

```javascript
// 自动判断是图片还是文档，选择最佳预览方式
await quickPreview('https://example.com/file.pdf')
```

### downloadOnly(url, fileName)

仅下载文件，不自动打开

```javascript
await downloadOnly('https://example.com/file.pdf', '自定义名称.pdf')
```

### silentDownload(url, options)

静默下载（无任何提示）

```javascript
const result = await silentDownload('https://example.com/file.pdf')
```

## 工具方法

### isImageFile(url)

检查文件是否为图片

```javascript
import { isImageFile } from '@/utils/downloadHelper'

if (isImageFile('https://example.com/photo.jpg')) {
  console.log('这是一个图片文件')
}
```

### isDocumentFile(url)

检查文件是否为文档

```javascript
import { isDocumentFile } from '@/utils/downloadHelper'

if (isDocumentFile('https://example.com/document.pdf')) {
  console.log('这是一个文档文件')
}
```

### getFileInfo(url)

获取文件详细信息

```javascript
import { getFileInfo } from '@/utils/downloadHelper'

const info = getFileInfo('https://example.com/document.pdf')
console.log(info)
// {
//   url: 'https://example.com/document.pdf',
//   name: 'document.pdf',
//   extension: '.pdf',
//   isImage: false,
//   isDocument: true,
//   type: 'document'
// }
```

## 实际应用场景

### 1. 文件列表页面

```javascript
export default {
  data() {
    return {
      fileList: [
        { name: '合同.pdf', url: 'https://example.com/contract.pdf' },
        { name: '图片.jpg', url: 'https://example.com/image.jpg' }
      ]
    }
  },
  methods: {
    async handleFileClick(file) {
      try {
        await this.$quickPreview(file.url)
      } catch (error) {
        uni.showToast({
          title: '打开失败',
          icon: 'none'
        })
      }
    }
  }
}
```

### 2. 图片画廊

```javascript
methods: {
  async saveAllImages() {
    const imageUrls = this.images.map(img => img.url)
    
    try {
      const result = await this.$downloadFiles(imageUrls, {
        saveToAlbum: true,
        showProgress: true
      })
      
      uni.showToast({
        title: `保存完成: ${result.successCount}/${result.total}`,
        icon: 'success'
      })
    } catch (error) {
      uni.showToast({
        title: '保存失败',
        icon: 'error'
      })
    }
  }
}
```

### 3. 文档管理

```javascript
methods: {
  async downloadDocument(url, fileName) {
    try {
      await this.$downloadFile(url, {
        fileName: fileName,
        autoOpen: true,
        loadingText: '下载文档中...'
      })
    } catch (error) {
      console.error('下载失败:', error)
    }
  }
}
```

## 错误处理

```javascript
try {
  const result = await downloadFile(url)
  console.log('下载成功:', result)
} catch (error) {
  console.error('下载失败:', error.message)
  
  // 根据错误类型处理
  if (error.message.includes('网络')) {
    uni.showToast({
      title: '网络连接失败',
      icon: 'none'
    })
  } else if (error.message.includes('权限')) {
    uni.showToast({
      title: '没有访问权限',
      icon: 'none'
    })
  }
}
```

## 注意事项

1. **网络权限**: 确保小程序有网络访问权限
2. **域名配置**: 下载域名需要在小程序后台配置
3. **文件大小**: 注意文件大小限制，避免下载过大文件
4. **用户体验**: 大文件下载时建议显示进度提示
5. **错误处理**: 做好网络异常和权限异常的处理

## 支持的文件类型

### 图片类型
- .jpg, .jpeg, .png, .gif, .bmp, .webp, .svg, .ico

### 文档类型  
- .pdf, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .txt, .rtf

其他类型文件会被识别为 'unknown' 类型，但仍可以下载。
