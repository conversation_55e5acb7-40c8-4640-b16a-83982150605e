<template>
  <view class="download-example">
    <view class="title">文件下载功能示例</view>
    
    <!-- 单个文件下载示例 -->
    <view class="section">
      <view class="section-title">1. 单个文件下载</view>
      <view class="button-group">
        <button @click="downloadSingleFile" class="btn">下载PDF文档</button>
        <button @click="previewImage" class="btn">预览图片</button>
        <button @click="saveImage" class="btn">保存图片到相册</button>
      </view>
    </view>
    
    <!-- 批量下载示例 -->
    <view class="section">
      <view class="section-title">2. 批量文件下载</view>
      <view class="button-group">
        <button @click="downloadMultipleFiles" class="btn">批量下载文件</button>
      </view>
    </view>
    
    <!-- 快速预览示例 -->
    <view class="section">
      <view class="section-title">3. 快速预览</view>
      <view class="button-group">
        <button @click="quickPreviewFile" class="btn">智能预览</button>
      </view>
    </view>
    
    <!-- 文件列表示例 -->
    <view class="section">
      <view class="section-title">4. 文件列表操作</view>
      <view class="file-list">
        <view 
          v-for="(file, index) in fileList" 
          :key="index"
          class="file-item"
          @click="handleFileClick(file)"
        >
          <image 
            v-if="isImageFile(file.url)" 
            class="file-icon" 
            src="/static/images/image-icon.png"
          ></image>
          <image 
            v-else 
            class="file-icon" 
            src="/static/images/document-icon.png"
          ></image>
          <view class="file-info">
            <text class="file-name">{{ file.name }}</text>
            <text class="file-type">{{ getFileType(file.url) }}</text>
          </view>
          <view class="file-actions">
            <button @click.stop="downloadFileOnly(file.url)" class="action-btn">下载</button>
            <button @click.stop="previewFileAction(file.url)" class="action-btn">预览</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
// 方式1: 导入具体方法（推荐）
import { 
  downloadFile, 
  previewFile, 
  saveImageToAlbum, 
  downloadFiles, 
  quickPreview,
  downloadOnly,
  isImageFile,
  getFileInfo
} from '@/utils/downloadHelper'

export default {
  name: 'DownloadExample',
  data() {
    return {
      // 示例文件列表
      fileList: [
        {
          name: '项目文档.pdf',
          url: 'https://example.com/document.pdf'
        },
        {
          name: '产品图片.jpg',
          url: 'https://example.com/image.jpg'
        },
        {
          name: 'Excel表格.xlsx',
          url: 'https://example.com/spreadsheet.xlsx'
        },
        {
          name: '演示文稿.pptx',
          url: 'https://example.com/presentation.pptx'
        }
      ]
    }
  },
  methods: {
    // 方式1: 使用导入的方法
    async downloadSingleFile() {
      try {
        const result = await downloadFile('https://example.com/document.pdf', {
          fileName: '自定义文件名.pdf',
          autoOpen: true
        })
        console.log('下载成功:', result)
      } catch (error) {
        console.error('下载失败:', error)
      }
    },
    
    // 方式2: 使用全局方法
    async previewImage() {
      try {
        // 使用全局方法
        const result = await this.$previewFile('https://example.com/image.jpg')
        console.log('预览成功:', result)
      } catch (error) {
        console.error('预览失败:', error)
      }
    },
    
    async saveImage() {
      try {
        const result = await saveImageToAlbum('https://example.com/image.jpg')
        console.log('保存成功:', result)
      } catch (error) {
        console.error('保存失败:', error)
      }
    },
    
    async downloadMultipleFiles() {
      const urls = [
        'https://example.com/file1.pdf',
        'https://example.com/file2.jpg',
        'https://example.com/file3.docx'
      ]
      
      try {
        const result = await downloadFiles(urls, {
          concurrent: 2, // 并发下载数量
          showProgress: true
        })
        console.log('批量下载结果:', result)
      } catch (error) {
        console.error('批量下载失败:', error)
      }
    },
    
    async quickPreviewFile() {
      try {
        // 智能预览，自动判断文件类型
        const result = await quickPreview('https://example.com/document.pdf')
        console.log('快速预览成功:', result)
      } catch (error) {
        console.error('快速预览失败:', error)
      }
    },
    
    // 处理文件点击
    async handleFileClick(file) {
      try {
        // 使用全局方法快速预览
        await this.$quickPreview(file.url)
      } catch (error) {
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        })
      }
    },
    
    // 仅下载不打开
    async downloadFileOnly(url) {
      try {
        await downloadOnly(url)
      } catch (error) {
        console.error('下载失败:', error)
      }
    },
    
    // 预览文件
    async previewFileAction(url) {
      try {
        await previewFile(url)
      } catch (error) {
        console.error('预览失败:', error)
      }
    },
    
    // 检查是否为图片
    isImageFile(url) {
      return isImageFile(url)
    },
    
    // 获取文件类型
    getFileType(url) {
      const fileInfo = getFileInfo(url)
      return fileInfo ? fileInfo.type : 'unknown'
    }
  }
}
</script>

<style scoped>
.download-example {
  padding: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
  color: #333;
}

.section {
  margin-bottom: 40rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #2c3e50;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.btn {
  padding: 20rpx 30rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.btn:active {
  background: #0056cc;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: white;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.file-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.file-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.file-type {
  font-size: 24rpx;
  color: #666;
}

.file-actions {
  display: flex;
  gap: 10rpx;
}

.action-btn {
  padding: 10rpx 20rpx;
  background: #f0f0f0;
  color: #333;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
}

.action-btn:active {
  background: #e0e0e0;
}
</style>
