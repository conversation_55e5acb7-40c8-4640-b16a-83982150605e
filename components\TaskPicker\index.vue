<template>
  <view class="picker">
    <uni-popup ref="popup" type="bottom">
      <view class="picker-content">
        <view class="picker-header">
          <text class="cancel-btn" @tap="handleCancel">取消</text>
          <text class="title">{{ title }}</text>
          <text class="confirm-btn" @tap="handleConfirm">确定</text>
        </view>
        <view class="picker-body">
          <scroll-view scroll-y style="max-height: 80vh;min-height: 30vh;overflow-y: auto;">
            <radio-group @change="handleChange">
              <label v-for="(item, index) in options" :key="index" class="radio-item"
                :style="{ paddingLeft: item.level * 30 + 30 + 'rpx' }">
                <view class="option-label">
                  <radio :value="String(item.id)" :checked="String(selectedValue) === String(item.id)"
                    color="#4080FF" />
                  <view>
                    <view class="option-text"><span class="option-text-label">来源：</span>{{ item.ranterName }}</view>
                    <view class="option-text"><span class="option-text-label">督办事项：</span>{{ item.rantContent }}</view>
                    <view class="option-text"><span class="option-text-label">责任人：</span>{{ item.responsiblePersonName }}
                    </view>
                    <view class="option-text"><span class="option-text-label">类型：</span>{{ formatDictTag(item.mattersType, 'rant_matters_type') }}</view>
                    <view class="option-text"><span class="option-text-label">分类：</span>{{ item.rantClassify }}</view>
                  </view>

                </view>
              </label>
            </radio-group>
          </scroll-view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { myTaskList } from '@/api/rant/matters';
import { getDicts } from '@/api/common';

export default {
  name: 'TaskPicker',
  props: {
    // 标题
    title: {
      type: String,
      default: '请选择任务'
    },
    // 自定义选项列表
    customOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      options: [
       /*  {
          ranterName: '张三',
          rantContent: '督办事项',
          responsiblePersonName: '李四',
          mattersType: '1,2',
          rantClassify: '分类'
        } */
      ],
      flattenOptions: [],
      selectedValue: '',
      selectedItem: null,
      selectedValues: [],
      selectedItems: [],
      pageNum: 1,
      pageSize: 10,
      hasMore: true,
      loading: false,
      inProgress: 1,
      dictData: {
        rant_matters_type: []
      }
    }
  },
  created() {
    this.getDict();
    this.loadTaskList()
    console.log(this.options);
  },
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.pageNum++;
      this.loadTaskList();
    }
  },
  methods: {
     // 获取数据字典
     async getDict() {
      try {
        const dictTypes = ['rant_matters_type'];
        const promises = dictTypes.map(type => getDicts(type));
        const results = await Promise.all(promises);
        
        results.forEach((res, index) => {
          if (res.code === 200) {
            this.dictData[dictTypes[index]] = res.data;
          }
        });
      } catch (error) {
        console.error('获取数据字典失败', error);
      }
    },
    
    // 格式化字典标签
    formatDictTag(value, dictType) {
      if (!value || !this.dictData[dictType]) return '';
      
      if (dictType === 'rant_matters_type' && value.includes(',')) {
        const types = value.split(',');
        return types.map(type => {
          const dictItem = this.dictData[dictType].find(item => item.dictValue === type);
          return dictItem ? dictItem.dictLabel : type;
        }).join(', ');
      } else {
        const dictItem = this.dictData[dictType].find(item => item.dictValue === value);
        return dictItem ? dictItem.dictLabel : value;
      }
    },
    // 加载部门数据
    async loadTaskList() {
      if (this.loading || !this.hasMore) return;

      this.loading = true;
      try {
        const res = await myTaskList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          inProgress: this.inProgress
        });

        if (res.code === 200) {
          const newList = res.rows || [];
          if (this.pageNum === 1) {
            this.options = newList;
          } else {
            this.options = [...this.options, ...newList];
          }

          // 判断是否还有更多数据
          this.hasMore = newList.length === this.pageSize;
        } else {
          uni.showToast({
            title: res.msg || '获取任务列表失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取任务列表失败:', error);
        uni.showToast({
          title: '获取任务列表失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 重置并重新加载
    resetAndLoad() {
      this.pageNum = 1;
      this.hasMore = true;
      this.options = [];
      this.loadTaskList();
    },

    // 扁平化处理树形数据，添加层级信息
    flattenTreeData(data) {
      this.flattenOptions = []

      const flatten = (items, level = 0) => {
        items.forEach(item => {
          // 复制一份数据，添加层级信息
          const flatItem = { ...item, level }
          this.flattenOptions.push(flatItem)

          // 递归处理子节点
          if (item.children && item.children.length > 0) {
            flatten(item.children, level + 1)
          }
        })
      }

      flatten(data)
    },

    // 设置自定义选项
    setOptions(options) {
      this.options = options
      this.flattenTreeData(options)
    },

    // 预选择值
    setSelectedValue(value) {
      this.selectedValue = value
      this.selectedItem = this.flattenOptions.find(item => item.id === value)
    },

    // 显示弹窗
    show() {
      this.resetAndLoad();
      this.$refs.popup.open('bottom')
    },

    // 处理选择变更
    handleChange(e) {
      const value = e.detail.value
      this.selectedValue = value
      this.selectedItem = this.options.find(item => item.id == value)

      // 选择后只更新选中状态，不关闭弹窗
      this.$emit('change', {
        id: this.selectedValue,
        name: this.selectedItem ? this.selectedItem.name : '',
        respPeople: this.selectedItem ? this.selectedItem.respPeople : null,
        respPeopleName: this.selectedItem ? this.selectedItem.respPeopleName : '',
        item: this.selectedItem
      })
    },

    // 处理确认
    handleConfirm() {
      if (!this.selectedValue) {
        uni.showToast({
          title: '请选择一项',
          icon: 'none'
        })
        return
      }

      this.$emit('confirm', {
        id: this.selectedValue,
        name: this.selectedItem ? this.selectedItem.name : '',
        respPeople: this.selectedItem ? this.selectedItem.respPeople : null,
        respPeopleName: this.selectedItem ? this.selectedItem.respPeopleName : '',
        item: this.selectedItem
      })
      this.$refs.popup.close()
    },

    // 处理取消
    handleCancel() {
      this.$refs.popup.close()
    }
  }
}
</script>

<style lang="scss" scoped>
.picker {
  width: 100%;
  // height: 100vh;
  overflow-y: auto;
}

.picker-content {
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  //overflow: hidden;
  max-height: calc(100vh - 200rpx);
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 90rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.cancel-btn,
.confirm-btn {
  font-size: 28rpx;
  padding: 10rpx;
  border-radius: 10rpx;
}

.cancel-btn {
  color: #666666;
}

.confirm-btn {
  /* color: #fff; */
  color: #4080FF;
}

.picker-body {
  padding: 20rpx;
  background-color: #D6E1F1;
}

.radio-item {
  display: block;
  padding: 20rpx;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
  margin-bottom: 10px;
  &:last-child {
    margin-bottom: 160rpx;
  }
}

.option-label {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #999999;
  .option-text-label {
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}

.option-text {
  font-size: 28rpx;
  color: #333333;
  margin-left: 10rpx;
}
</style>
