.plus-icon {
  font-size: 24px;
  color: #fff;
  margin-bottom: 5px;
}
// 吐槽状态
.task-status {
  font-size: 28rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  height: fit-content;
}

.status-draft {
  color: #4080FF;
  background: rgba(64,128,255,0.1);
}

.status-delayed-unfinished {
  color: #CC1414;
  background: rgba(204,20,20,0.1);
}

.status-delayed-finished {
  color: #FF9900;
  background: rgba(255,149,0,0.1);
 
}

.status-in-progress {
  color: #376DF7;
  background: rgba(55,109,247,0.1);
 
}

.status-on-time {
  color: #26BF73;
  background: rgba(38,191,115,0.1);
}

.status-ended {
  color: #999;
  background: rgba(102,102,102,0.1);
}

.status-approving {
  color: #FAAD14;
  background: rgba(255,149,0,0.1);
}

.status-rejected {
  color: #FF4D4F;
  background: rgba(255,77,79,0.1);
}

.custom-arrow-right{
  display: flex;
  align-items: center;
}
.custom-arrow-right::after{
  content: '';
  display: inline-block;
  width: 32rpx;
  height: 32rpx;
  background: url('/static/images/icon-arrow.png') no-repeat center center;
  background-size: 100% 100%;
  margin-left: 10rpx;
}

// 搜索组件样式
.filter-area {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFFFFF;
  border-radius: 32rpx;
  padding: 10rpx 30rpx;
  margin-right: 16rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  height: 60rpx;
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #F5F5F5;
  border-radius: 30rpx;
  padding: 0 20rpx;
  margin-left: 10rpx;
  height: 60rpx;
  box-sizing: border-box;
}

.icon-search {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
}

.clear-icon {
  font-size: 32rpx;
  color: #999;
  padding: 0 10rpx;
}
.cu-flex-between{
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 列表卡片样式
.task-list {
  padding: 0;
}
.task-card {
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.task-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.task-title {
  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
}

.task-date {
  font-size: 28rpx;
  color: #999;
}

.task-subheader {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.person-type {
  display: flex;
}

.task-person, .task-type {
  font-size: 28rpx;
  color: #666;
  margin-right: 16rpx;
}



.task-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

.required-mark {
  color: #FF5151;
  font-size: 28rpx;
  margin-left: 4rpx;
}
.keyboard-active {
  margin-bottom: 30vh;
}