<template>
  <view class="rant-detail-container">
    <!-- 基本信息卡片 -->
    <view class="detail-card">
      <view class="card-header">
        <image class="header-icon" src="/static/images/icon-baseinfo.png" mode="aspectFit"></image>
        <text class="header-title">基本信息</text>
      </view>
      
      <view class="info-item" v-if="showRanterName">
        <text class="info-label">来源</text>
        <text class="info-value">{{ rantDetail.ranterName || '--' }}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">类型</text>
        <view class="info-value">
          <view v-for="(type, index) in rantDetail.mattersType ? rantDetail.mattersType.split(',') : []" :key="index" class="type-tag">
            {{ getMattersTypeLabel(type) }}
          </view>
        </view>
      </view>
      
      <view class="info-item">
        <text class="info-label">分类</text>
        <text class="info-value">{{ getClassifyLabel(rantDetail.rantClassify) || '--' }}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">状态</text>
        <view class="info-value">
          <text :class="[getStatusClass(rantDetail.status)]">{{ getStatusLabel(rantDetail.status) }}</text>
        </view>
      </view>
      
      <view class="info-item">
        <text class="info-label">责任部门</text>
        <text class="info-value">{{ rantDetail.deptName || '--' }}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">责任人</text>
        <text class="info-value">{{ rantDetail.responsiblePersonName || '--' }}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">责任部门负责人</text>
        <text class="info-value">{{ rantDetail.respDeptResponsiblerName || '--' }}</text>
      </view>
      
      <view class="info-item" v-if="showRespDeptLeader">
        <text class="info-label">分管领导</text>
        <text class="info-value">{{ rantDetail.respDeptLeaderName || '--' }}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">计划完成时间</text>
        <text class="info-value">{{ rantDetail.planTime || '--' }}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">是否私密</text>
        <text class="info-value">{{ rantDetail.isPrivate == 1 ? '是' : '否' }}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">可见范围</text>
        <text class="info-value">{{ rantDetail.visibleScope || '所有人' }}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">是否结项</text>
        <text class="info-value">{{ rantDetail.isCompletion ? '是' : '否' }}</text>
      </view>
      
      <view class="info-item content-item">
        <text class="info-label">内容</text>
        <text class="info-value content-value">{{ rantDetail.rantContent || '--' }}</text>
      </view>
      
      <view class="info-item content-item">
        <text class="info-label">措施</text>
        <text class="info-value content-value">{{ rantDetail.solution || '--' }}</text>
      </view>
      
      <view class="info-item" v-if="rantDetail.isCompletion">
        <text class="info-label">结项时间</text>
        <text class="info-value">{{ rantDetail.closingTime || '--' }}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">成果</text>
        <view class="info-value">
          <view v-for="(file, index) in rantDetail.fileList" :key="index" class="file-name" @tap="openFile(file.url)">
            <view class="filename-part">{{ getTruncatedFileName(file.name) }}</view>
            <view class="extension-part">{{ getFileExtension(file.name) }}</view>
          </view>
          <text v-if="!rantDetail.fileList || rantDetail.fileList.length === 0">--</text>
        </view>
      </view>
      
      <view class="info-item content-item">
        <text class="info-label">最新进展</text>
        <rich-text class="info-value content-value" :nodes="rantDetail.thisProgress || '--'"></rich-text>
      </view>
    </view>
    
    <!-- 进度情况卡片 -->
    <view class="detail-card">
      <view class="card-header">
        <image class="header-icon" src="/static/images/icon-progress.png" mode="aspectFit"></image>
        <text class="header-title">进度情况</text>
      </view>
      
      <view class="progress-list">
        <view class="progress-item" v-for="(item, index) in recordDtoList" :key="index">
          <view class="progress-header">
            <view class="progress-index">{{ index + 1 }}</view>
            <view class="progress-date">{{ formatDate(item.feedbackTime) }}</view>
          </view>
          <view class="progress-content">
            <rich-text class="progress-text" :nodes="item.actualProgress"></rich-text>
            <view class="progress-files">
              <view v-for="(file, fileIndex) in item.fileList" :key="fileIndex" class="file-name" @tap="openFile(file.url)">
                <view class="filename-part">{{ getTruncatedFileName(file.name) }}</view>
                <view class="extension-part">{{ getFileExtension(file.name) }}</view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="empty-state" v-if="!recordDtoList || recordDtoList.length === 0">
          <text>暂无进度记录</text>
        </view>
      </view>
    </view>
    
    <!-- 评价信息卡片 -->
    <view class="detail-card">
      <view class="card-header cu-flex">
        <view>
          <image class="header-icon" src="/static/images/icon-rate.png" mode="aspectFit"></image>
          <text class="header-title">评价信息</text>
        </view>

        <view>
          <view class="">
            <view class="rating-stars cu-flex">
              <uni-rate allow-half :value="evaluateAvgScore/2" size="16" :readonly="true"/>
              <!-- <text v-for="star in generateStars(evaluateAvgScore/2)" :key="star.id" :class="star.class">★</text> -->
              <text class="rating-score">{{ evaluateAvgScore }}分</text>
            </view>
          </view>
        </view>
        
      </view>
      
      
      
      <view class="rating-list">
        <view class="rating-item" v-for="(item, index) in rantEvaluativeInfoDtoList" :key="index">
          <view class="rating-header cu-flex">
            <view style="flex: 1.1;">
              <view class="rating-stars small cu-flex">
                <uni-rate allow-half :value="item.score/2" size="16" :readonly="true"/>
                <!-- <text v-for="star in generateStars(item.score/2)" :key="star.id" :class="star.class">★</text> -->
               <text class="rating-score">{{ item.score }}分</text>
              </view>
            </view>
            <view style="flex: 1;">
              <view class="rating-info cu-flex">
                <text class="rating-user">评价人：{{ item.evaluatorName || '--' }}</text>
                <text class="rating-date">{{ item.createTime}}</text>
              </view>
            </view>
          </view>
          <view class="rating-content">
            <text class="rating-text">{{ item.evaluationContent || '--' }}</text>
           
          </view>
        </view>
        
        <view class="empty-state" v-if="!rantEvaluativeInfoDtoList || rantEvaluativeInfoDtoList.length === 0">
          <text>暂无评价信息</text>
        </view>
      </view>
    </view>
    
    <!-- Loading Indicator -->
    <view class="loading-overlay" v-if="loading || evaluativeLoading">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</template>

<script>
import { lookRankMatter } from '../../api/rant/matters.js';
import { evaluativeList, avgScore } from '../../api/rant/evaluative.js';
import { getDicts } from '../../api/common.js';
import { rantStatusOption } from '../../constant/index.js';
import { getStatusClass } from '@/utils/utils';
// 状态选项定义 - 与PC端保持一致
/* const rantStatusOption = [
  // 0草稿 -蓝色 ，1进行中 -黄色，2按时完成 -绿色，3延期完成-浅红，4延期未完成-深红，5终止-灰色, 6审批中，7驳回
  {label: '草稿', value: 0, type: 'info'},
  {label: '进行中', value: 1, type: 'info', color: 'yellow'},
  {label: '按时完成', value: 2, type: 'success'},
  {label: '延期完成', value: 3, type: 'danger'},
  {label: '延期未完成', value: 4, type: 'success', color: 'red', fontColor: 'white!important'},
  {label: '终止', value: 5, type: 'info'},
  {label: '审批中', value: 6, type: 'warning'},
  {label: '驳回', value: 7, type: 'success', color: 'red', fontColor: 'white!important'},
]; */

export default {
  data() {
    return {
      id: null,
      loading: false,
      evaluativeLoading: false,
      rantDetail: {
        mattersType: ""
      },
      recordDtoList: [],
      rantEvaluativeInfoDtoList: [],
      evaluateAvgScore: 0,
      type: 'matters',
      // 字典数据
      dictData: {
        rant_classify: [],
        rant_matters_type: []
      },
      rantStatusOption: rantStatusOption, // 添加状态选项到data中
      pageSize: 10,
      pageNum: 1,
      total: 0,
      hasMore: true,
      isRefreshing: false,
      showRanterName: false // 默认不显示来源
    }
  },
  computed: {
    showRespDeptLeader() { // 是否显示分管领导
      // console.log('this.rantDetail.mattersType', this.rantDetail.mattersType, this.rantDetail.mattersType.includes('3'));
      return this.rantDetail.mattersType?.includes('3');
    }
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id;
      this.showRanterName = options.showRanterName === 'true';
      // 获取字典数据
      this.initDictData();
    }
  },
  onPullDownRefresh() {
    this.isRefreshing = true;
    this.pageNum = 1;
    this.hasMore = true;
    this.evaluativeList = [];
    Promise.all([
      this.fetchFeedbackDetail(),
      this.fetchEvaluativeList()
    ]).finally(() => {
      this.isRefreshing = false;
      uni.stopPullDownRefresh();
    });
  },
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.pageNum++;
      this.fetchEvaluativeList();
    }
  },
  methods: {
    getTruncatedFileName(fileName) {
      const extension = this.getFileExtension(fileName);
      return fileName.replace(extension, '');
    },
    getFileExtension(fileName) {
      const lastDotIndex = fileName.lastIndexOf('.');
      if (lastDotIndex === -1) return '';
      return fileName.substring(lastDotIndex);
    },
    fetchAvgScore() {
      avgScore(this.id).then(res => {
        if (res.code === 200) {
          this.evaluateAvgScore = res.data;
        }
      });
    },
    // 初始化字典数据
    initDictData() {
      this.fetchAvgScore();
      // 获取吐槽分类字典
      getDicts('rant_classify').then(res => {
        if (res.code === 200) {
          this.dictData.rant_classify = res.data;
          // 加载督办事项类型字典
          return getDicts('rant_matters_type');
        }
      }).then(res => {
        if (res && res.code === 200) {
          this.dictData.rant_matters_type = res.data;
          // 加载详情数据
          this.loadDetailData();
        }
      }).catch(err => {
      });
    },
    
    // 加载详情数据
    loadDetailData() {
      this.loading = true;
      // 获取督办事项详情
      lookRankMatter(this.id).then(res => {
        if (res.code === 200) {
          this.rantDetail = res.data;
          this.recordDtoList = res.data.recordDtoList || [];
        } else {
          uni.showToast({
            icon: 'none',
            title: res.msg || '加载详情失败'
          });
        }
        
      }).catch(err => {
        uni.showToast({
          icon: 'none',
          title: '加载数据失败，请稍后重试'
        });
      }).finally(() => {
        this.loading = false;
      });
       // 获取评价列表
       this.fetchEvaluativeList();
    },
    
    // 获取事项类型字典标签
    getMattersTypeLabel(value) {
      const item = this.dictData.rant_matters_type.find(item => item.dictValue === value);
      return item ? item.dictLabel : value;
    },
    
    // 获取分类字典标签
    getClassifyLabel(value) {
      const item = this.dictData.rant_classify.find(item => item.dictValue === value);
      return item ? item.dictLabel : value;
    },
    
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '--';
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return dateStr;
      
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    },
    
    // 生成评分星星
    generateStars(rating) {
      const stars = [];
      for (let i = 1; i <= 5; i++) {
        let starClass = 'star';
        if (rating >= i) {
          starClass = 'star filled';
        } else if (rating > i - 0.5) {
          starClass = 'star half-filled';
        }
        stars.push({
          id: `star-${i}`,
          class: starClass
        });
      }
      return stars;
    },
    
    // 打开文件
    openFile(url) {
      if (!url) return;
      
      uni.showLoading({
        title: '打开中...'
      });
      
      // 检查文件类型
      const fileType = this.getFileType(url);
      
      // 如果是图片，直接使用预览图片
      if (fileType === 'image') {
        uni.previewImage({
          urls: [url],
          current: url,
          success: () => {
            uni.hideLoading();
          },
          fail: () => {
            uni.hideLoading();
            uni.showToast({
              icon: 'none',
              title: '预览图片失败'
            });
          }
        });
        return;
      }
      
      // 其他类型文件使用下载后打开
      uni.downloadFile({
        url: url,
        success: (res) => {
          console.log('res', res);
          uni.hideLoading();
          if (res.statusCode === 200) {
            uni.openDocument({
              filePath: res.tempFilePath,
              fileType: fileType,
              success: () => {
                console.log('文件打开成功');
              },
              fail: (error) => {
                console.error('打开文件失败:', error);
                uni.showToast({
                  icon: 'none',
                  title: '打开文件失败'
                });
              }
            });
          } else {
            uni.showToast({
              icon: 'none',
              title: '下载文件失败'
            });
          }
        },
        fail: (error) => {
          uni.hideLoading();
          console.error('下载文件失败:', error);
          uni.showToast({
            icon: 'none',
            title: '下载文件失败'
          });
        }
      });
    },
    
    // 获取文件类型
    getFileType(url) {
      const extension = url.split('.').pop().toLowerCase();
      const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
      const documentTypes = {
        'pdf': 'pdf',
        'doc': 'doc',
        'docx': 'docx',
        'xls': 'xls',
        'xlsx': 'xlsx',
        'ppt': 'ppt',
        'pptx': 'pptx',
        'txt': 'txt'
      };
      
      if (imageTypes.includes(extension)) {
        return 'image';
      }
      
      return documentTypes[extension] || 'unknown';
    },
    
    // Add new method to get status label from rantStatusOption
    getStatusLabel(value) {
      const statusItem = this.rantStatusOption.find(item => item.value === Number(value));
      return statusItem ? statusItem.label : '未知状态';
    },
    getStatusClass(status) {
      return getStatusClass(status);
    },
    async fetchEvaluativeList() {
      if (this.evaluativeLoading || !this.hasMore) return;
      
      this.evaluativeLoading = true;
      try {
        const res = await evaluativeList({
          rantMattersId: this.id,
          pageNum: this.pageNum,
          pageSize: this.pageSize
        });
        
        if (res.code === 200) {
          const newList = res.rows || [];
          if (this.pageNum === 1) {
            this.rantEvaluativeInfoDtoList = newList;
          } else {
            this.rantEvaluativeInfoDtoList = [...this.rantEvaluativeInfoDtoList, ...newList];
          }
          
          // 判断是否还有更多数据
          this.hasMore = newList.length === this.pageSize;
        } else {
          uni.showToast({
            title: res.msg || '获取评价列表失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取评价列表失败', error);
        uni.showToast({
          title: '获取评价列表失败',
          icon: 'none'
        });
      } finally {
        this.evaluativeLoading = false;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
 .file-name {
    width: 100%;
    display: flex;
    color: #4080FF;
    line-height: 1.8;
    .filename-part{
      min-width: 0;
      flex-grow: 1;
      word-break: keep-all;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #4080FF;
    }
    .extension-part{
      word-break: keep-all;
    }
  }
.cu-flex{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.rant-detail-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding: 15px;
}

/* 卡片通用样式 */
.detail-card {
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #F0F0F0;
}

.header-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 20rpx;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}

/* 基本信息样式 */
.info-item {
  display: flex;
  margin-bottom: 12px;
  font-size: 14px;
}

.info-label {
  width: 200rpx;
  color: #666666;
  font-weight: 400;
  word-wrap: keep-all;
  white-space: nowrap;
}

.info-value {
  flex: 1;
  color: #333333;
  font-weight: 600;
  min-width: 0;
}

.content-item {
  align-items: flex-start;
}

.content-value {
  line-height: 1.6;
}

.type-tag {
  display: inline-block;
  padding: 2px 6px;
  margin-right: 5px;
  margin-bottom: 5px;
  background-color: #E8F4FF;
  color: #4080FF;
  border-radius: 4px;
  font-size: 12px;
}

.file-link {
  max-width: 430rpx;
  color: #4080FF;
  line-height: 1.8;
  word-wrap: keep-all;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

/* 进度情况样式 */
.progress-list {
  padding: 5px 0;
}

.progress-item {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #F0F0F0;
}

.progress-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.progress-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.progress-index {
  width: 20px;
  height: 20px;
  background-color: #4080FF;
  color: #FFFFFF;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  margin-right: 10px;
}

.progress-date {
  font-size: 12px;
  color: #999999;
}

.progress-content {
  padding-left: 30px;
}

.progress-text {
  font-size: 14px;
  color: #333333;
  line-height: 1.6;
  margin-bottom: 8px;
}

.progress-files {
  margin-top: 5px;
}

/* 评价信息样式 */
.rating-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.rating-list {
  padding: 5px 0;
}

.rating-item {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #F0F0F0;
}

.rating-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.rating-stars {
  display: flex;
  justify-content: flex-start;
  margin-right: 10px;
}

.rating-stars.small {
  transform: scale(0.8);
  transform-origin: left;
}

.star {
  font-size: 18px;
  color: #DDDDDD;
  margin-right: 2px;
}

.star.filled {
  color: #FFCC00;
}

.star.half-filled {
  position: relative;
  color: #DDDDDD;
}

.star.half-filled:after {
  content: '★';
  position: absolute;
  left: 0;
  top: 0;
  width: 50%;
  overflow: hidden;
  color: #FFCC00;
}

.rating-score {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
  word-break: keep-all;
}

.rating-content {
  // padding-left: 30px;
}

.rating-text {
  font-size: 14px;
  color: #333333;
  line-height: 1.6;
  margin-bottom: 8px;
}

.rating-info {
  display: flex;
  flex-wrap: wrap;
  margin-top: 5px;
}

.rating-user, .rating-date {
  font-size: 12px;
  color: #999999;
  margin-right: 15px;
  width: 100%;
  text-align: right;
  word-break: keep-all;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 20px 0;
  color: #999999;
  font-size: 14px;
}

/* 加载动画 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid #f3f3f3;
  border-top-color: #4080FF;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 15px;
  font-size: 14px;
  color: #666666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Add status tag styles */
.status-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #fff;
}

.status-success {
  background-color: #52c41a;
}

.status-info {
  background-color: #1890ff;
}

.status-warning {
  background-color: #faad14;
}

.status-danger {
  background-color: #f5222d;
}

/* Custom status colors */
.status-custom-yellow {
  background-color: #ffcb2f;
}

.status-custom-red {
  background-color: #ff4d4f;
}

.status-custom-blue {
  background-color: #1890ff;
}

.status-custom-green {
  background-color: #52c41a;
}

.status-custom-gray {
  background-color: #999999;
}
</style>
