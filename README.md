# 督办系统微信小程序

## 项目简介

督办系统微信小程序是一个用于企业内部督办事项管理的移动端应用。基于uniapp框架开发，支持微信小程序、鸿蒙系统等多平台。

## 主要功能

### 1. 督办事项管理
- **查看督办**: 浏览所有督办事项，支持筛选和搜索
- **我的吐槽**: 管理个人发起的吐槽事项
- **我的反馈**: 查看和处理分配给自己的督办任务
- **我的任务**: 管理个人负责的督办任务

### 2. 事项操作
- **创建督办**: 发起新的督办事项
- **进度反馈**: 更新任务执行进度
- **事项评价**: 对完成的督办事项进行评价
- **状态管理**: 跟踪督办事项的各种状态

### 3. 用户交互
- **多维筛选**: 按类型、状态、责任人等条件筛选
- **搜索功能**: 支持关键词搜索督办内容
- **文件上传**: 支持图片、文档等文件上传
- **实时反馈**: 即时更新任务状态和进度

### 4. 审批流程 (新增)
- **时间线展示**: 以时间线形式展示审批流程
- **状态管理**: 支持审批中、同意、驳回等状态
- **审批信息**: 显示审批人、部门、时间、意见等详细信息
- **移动端优化**: 专为移动端设计的审批流程界面

## 技术特色

### 跨平台兼容性
- **鸿蒙系统适配**: 专门针对鸿蒙系统进行了兼容性优化
- **滚动功能增强**: 自定义滚动工具，解决不同平台滚动兼容性问题
- **多端统一**: 使用uniapp框架，确保多平台一致的用户体验

### 滚动工具 (scrollHelper)
为解决鸿蒙系统等平台的滚动兼容性问题，项目内置了智能滚动工具：

#### 主要功能:
- **平台检测**: 自动检测运行环境（鸿蒙、微信、H5等）
- **多方案回退**: 提供3种滚动实现方案，自动选择最佳方案
- **智能兼容**: 针对不同平台使用最适合的滚动方法

#### 使用方法:
```javascript
// 滚动到页面底部
this.$scrollHelper.scrollToBottom({
  duration: 300,
  success: () => console.log('滚动成功'),
  fail: (error) => console.warn('滚动失败:', error)
});

// 滚动到页面顶部
this.$scrollHelper.scrollToTop({
  duration: 300
});

// 滚动到指定位置
this.$scrollHelper.scrollTo({
  scrollTop: 500,
  duration: 300
});

// 滚动到指定元素
this.$scrollHelper.scrollToElement('.target-element', {
  duration: 300,
  offset: 10
});
```

#### 兼容方案:
1. **标准方案**: 使用 `uni.pageScrollTo` API
2. **查询方案**: 使用 `createSelectorQuery` 获取页面高度后滚动
3. **DOM方案**: H5环境下使用原生DOM滚动，支持平滑动画

### 审批流程组件 (ApprovalTimeline)
为移动端设计的审批流程时间线组件，完美适配当前项目风格：

#### 主要功能:
- **时间线展示**: 垂直时间线，清晰展示审批流程
- **状态图标**: 不同状态使用不同颜色和图标
- **用户头像**: 自动生成彩色头像，提升视觉效果
- **响应式设计**: 适配不同屏幕尺寸

#### 使用方法:
```vue
<template>
  <ApprovalTimeline :approvalList="approvalList" />
</template>

<script>
import ApprovalTimeline from '@/components/ApprovalTimeline/index.vue';

export default {
  components: {
    ApprovalTimeline
  },
  data() {
    return {
      approvalList: [] // 审批流程数据
    }
  },
  methods: {
    async fetchApprovalRecord(mattersId) {
      const res = await getApproverRecord(mattersId);
      this.approvalList = res.data || [];
    }
  }
}
</script>
```

#### 数据格式:
```javascript
[
  {
    approveNickName: '张三',        // 审批人姓名
    approveTitle: '部门负责人',     // 审批人角色
    approveUserDept: '技术部',       // 审批人部门
    approveStatus: 2,               // 审批状态 (1-审批中 2-同意 3-驳回)
    finishTime: '2023-12-01 14:30', // 审批时间
    approveDesc: '同意该方案'        // 审批意见
  }
]
```

## 项目结构

```
zj-company-rant-report/
├── api/                    # API接口
│   ├── rant/              # 督办相关接口
│   └── system/            # 系统相关接口
├── components/            # 公共组件
│   ├── ApprovalTimeline/  # 审批流程时间线组件 (新增)
├── pages/                 # 页面文件
│   ├── feedback/          # 反馈页面
│   ├── index/             # 首页
│   ├── look/              # 查看页面
│   ├── matters/           # 督办事项
│   ├── myFeedback/        # 我的反馈
│   ├── myRant/            # 我的吐槽
│   ├── myTask/            # 我的任务
│   └── taskList/          # 任务列表
├── utils/                 # 工具函数
│   └── scrollHelper.js    # 滚动工具 (新增)
├── static/                # 静态资源
├── store/                 # 状态管理
└── uni_modules/           # uni组件库
```

## 开发环境

- **框架**: uniapp
- **语言**: JavaScript/TypeScript
- **UI**: uni-ui 组件库
- **状态管理**: Vuex
- **构建工具**: HBuilderX

## 安装与运行

1. 克隆项目
```bash
git clone [项目地址]
cd zj-company-rant-report
```

2. 安装依赖
```bash
npm install
```

3. 运行项目
```bash
# 微信小程序
npm run dev:mp-weixin

# H5
npm run dev:h5

# APP
npm run dev:app
```

## 部署说明

### 微信小程序
1. 在 HBuilderX 中选择 "发行" -> "小程序-微信"
2. 上传到微信开发者工具
3. 提交审核并发布

### 鸿蒙系统
1. 确保已配置鸿蒙开发环境
2. 运行 `npm run dev:app` 生成APP包
3. 按照鸿蒙应用发布流程提交

## 更新日志

### v1.2.0 (最新)
- ✅ 新增审批流程功能，支持时间线展示
- ✅ 审批流程支持状态显示（审批中、同意、驳回）
- ✅ 移动端优化的审批流程UI设计
- ✅ 集成到督办详情和反馈页面

### v1.1.0
- ✅ 新增兼容鸿蒙系统的滚动工具
- ✅ 优化评价功能，支持星级评分
- ✅ 改进多平台兼容性
- ✅ 修复若干已知问题

### v1.0.0
- ✅ 基础督办系统功能
- ✅ 用户权限管理
- ✅ 文件上传功能
- ✅ 多状态流转

## 注意事项

1. **鸿蒙系统**: 项目已针对鸿蒙系统进行专门优化，建议使用最新版本
2. **权限配置**: 需要配置文件上传、拍照等相关权限
3. **API配置**: 请在 `config/` 目录下配置正确的API地址
4. **性能优化**: 大列表建议使用虚拟滚动以提升性能

## 联系方式

如有问题或建议，请联系开发团队。

---

**开发团队**: AI进化论-花生  
**版权所有**: 引用请注明出处