/**
 * 文件选择工具 - 针对iOS端优化
 * 解决iOS端文件选择失效问题
 * 版本: v1.0.0
 */

/**
 * 智能文件选择器
 * 根据平台和环境选择最佳的文件选择方案
 */
class FileHelper {
  constructor() {
    this.platform = this.getPlatform()
    this.isIOS = this.platform.isIOS
    this.isAndroid = this.platform.isAndroid
    this.isMiniProgram = this.platform.isMiniProgram
  }

  /**
   * 获取平台信息
   */
  getPlatform() {
    const platform = uni.getSystemInfoSync().platform
    const appPlatform = uni.getSystemInfoSync().appPlatform || ''
    
    return {
      isIOS: platform === 'ios',
      isAndroid: platform === 'android',
      isMiniProgram: typeof wx !== 'undefined',
      isH5: typeof window !== 'undefined',
      platform: platform,
      appPlatform: appPlatform
    }
  }

  /**
   * 智能文件选择
   * @param {Object} options 选择配置
   * @returns {Promise} 选择结果
   */
  async chooseFile(options = {}) {
    const defaultOptions = {
      count: 5,
      type: 'all',
      extension: [],
      showChooseDialog: true
    }
    
    const config = { ...defaultOptions, ...options }
    
    try {
      // 小程序环境
      if (this.isMiniProgram) {
        return await this.chooseMessageFile(config)
      }
      
      // H5环境
      if (this.platform.isH5) {
        return await this.chooseFileH5(config)
      }
      
      // App环境 - iOS需要特殊处理
      if (this.isIOS) {
        return await this.chooseFileIOS(config)
      }
      
      // Android默认方案
      return await this.chooseFileDefault(config)
      
    } catch (error) {
      console.error('文件选择失败:', error)
      throw error
    }
  }

  /**
   * 微信小程序文件选择 - iOS优化
   */
  async chooseMessageFile(config) {
    return new Promise((resolve, reject) => {
      // iOS端扩展名格式优化
      let extension = []
      if (config.extension && config.extension.length > 0) {
        extension = this.formatExtensionForIOS(config.extension)
      }

      // 显示选择对话框
      if (config.showChooseDialog && this.isIOS) {
        uni.showModal({
          title: '选择文件',
          content: 'iOS设备需要从微信聊天记录中选择文件',
          showCancel: true,
          confirmText: '继续',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              this.performChooseMessageFile(config, extension, resolve, reject)
            } else {
              reject(new Error('用户取消选择'))
            }
          }
        })
      } else {
        this.performChooseMessageFile(config, extension, resolve, reject)
      }
    })
  }

  /**
   * 执行微信聊天文件选择
   */
  performChooseMessageFile(config, extension, resolve, reject) {
    const chooseOptions = {
      count: config.count,
      type: config.type === 'image' ? 'image' : 
            config.type === 'video' ? 'video' : 'file',
      success: (res) => {
        if (res.tempFiles && res.tempFiles.length > 0) {
          const files = res.tempFiles.map(file => ({
            path: file.path,
            size: file.size,
            name: file.name || this.generateFileName(file.path),
            type: file.type || this.getFileTypeFromPath(file.path)
          }))
          resolve({ tempFiles: files })
        } else {
          reject(new Error('未选择任何文件'))
        }
      },
      fail: (err) => {
        console.error('chooseMessageFile失败:', err)
        // iOS端回退方案
        if (this.isIOS) {
          this.chooseImageFallback(config, resolve, reject)
        } else {
          reject(err)
        }
      }
    }

    // 添加扩展名筛选
    if (extension.length > 0) {
      chooseOptions.extension = extension
    }

    // 使用原生微信API
    if (typeof wx !== 'undefined' && wx.chooseMessageFile) {
      wx.chooseMessageFile(chooseOptions)
    } else {
      // uniapp封装的API
      uni.chooseMessageFile(chooseOptions)
    }
  }

  /**
   * iOS端扩展名格式化
   * 解决iOS端扩展名格式问题
   */
  formatExtensionForIOS(extensions) {
    const formatted = []
    
    extensions.forEach(ext => {
      // 确保扩展名格式正确
      let cleanExt = ext.trim().toLowerCase()
      
      // 添加带点和不带点的版本
      if (cleanExt.startsWith('.')) {
        formatted.push(cleanExt)        // .pdf
        formatted.push(cleanExt.slice(1)) // pdf
      } else {
        formatted.push(cleanExt)          // pdf
        formatted.push('.' + cleanExt)    // .pdf
      }
    })
    
    return [...new Set(formatted)] // 去重
  }

  /**
   * iOS专用文件选择
   */
  async chooseFileIOS(config) {
    // iOS App端暂时使用图片选择作为回退
    return new Promise((resolve, reject) => {
      uni.showActionSheet({
        itemList: ['选择图片', '选择视频'],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.chooseImageFallback(config, resolve, reject)
          } else if (res.tapIndex === 1) {
            this.chooseVideoFallback(config, resolve, reject)
          }
        },
        fail: reject
      })
    })
  }

  /**
   * 图片选择回退方案
   */
  chooseImageFallback(config, resolve, reject) {
    uni.chooseImage({
      count: config.count,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const files = res.tempFilePaths.map((path, index) => ({
          path: path,
          size: res.tempFiles ? res.tempFiles[index]?.size || 0 : 0,
          name: this.generateFileName(path),
          type: 'image'
        }))
        resolve({ tempFiles: files })
      },
      fail: reject
    })
  }

  /**
   * 视频选择回退方案
   */
  chooseVideoFallback(config, resolve, reject) {
    uni.chooseVideo({
      sourceType: ['album', 'camera'],
      maxDuration: 60,
      camera: 'back',
      success: (res) => {
        const file = {
          path: res.tempFilePath,
          size: res.size || 0,
          name: this.generateFileName(res.tempFilePath),
          type: 'video',
          duration: res.duration
        }
        resolve({ tempFiles: [file] })
      },
      fail: reject
    })
  }

  /**
   * H5环境文件选择
   */
  async chooseFileH5(config) {
    return new Promise((resolve, reject) => {
      uni.chooseFile({
        count: config.count,
        type: config.type,
        extension: config.extension,
        success: resolve,
        fail: reject
      })
    })
  }

  /**
   * 默认文件选择方案
   */
  async chooseFileDefault(config) {
    return new Promise((resolve, reject) => {
      uni.chooseFile({
        count: config.count,
        type: config.type,
        extension: config.extension,
        success: resolve,
        fail: (err) => {
          // 失败时尝试图片选择
          this.chooseImageFallback(config, resolve, reject)
        }
      })
    })
  }

  /**
   * 根据文件路径生成文件名
   */
  generateFileName(path) {
    const timestamp = Date.now()
    const extension = this.getExtensionFromPath(path)
    return `file_${timestamp}${extension}`
  }

  /**
   * 从路径获取文件扩展名
   */
  getExtensionFromPath(path) {
    const lastDot = path.lastIndexOf('.')
    return lastDot > -1 ? path.substring(lastDot) : ''
  }

  /**
   * 从路径获取文件类型
   */
  getFileTypeFromPath(path) {
    const extension = this.getExtensionFromPath(path).toLowerCase()
    
    const typeMap = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg', 
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.mp4': 'video/mp4',
      '.mov': 'video/quicktime',
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    }
    
    return typeMap[extension] || 'application/octet-stream'
  }

  /**
   * 批量上传文件
   */
  async uploadFiles(files, uploadUrl, options = {}) {
    const defaultOptions = {
      name: 'file',
      formData: {},
      header: {}
    }

    const config = { ...defaultOptions, ...options }
    const uploadPromises = []

    for (let i = 0; i < files.length; i++) {
      const file = files[i]

      const uploadPromise = new Promise((resolve, reject) => {
        uni.uploadFile({
          url: uploadUrl,
          filePath: file.path,
          name: config.name,
          formData: {
            ...config.formData,
            fileName: file.name,
            fileType: file.type
          },
          header: config.header,
          success: (res) => {
            try {
              const result = JSON.parse(res.data)
              resolve(result)
            } catch (e) {
              resolve({ code: 200, data: res.data })
            }
          },
          fail: reject
        })
      })

      uploadPromises.push(uploadPromise)
    }

    return Promise.all(uploadPromises)
  }

  /**
   * 智能文件下载器
   * 支持微信小程序和企业微信环境
   * @param {String} url 文件下载地址
   * @param {Object} options 下载配置选项
   * @returns {Promise} 下载结果
   */
  async downloadFile(url, options = {}) {
    const defaultOptions = {
      showLoading: true,
      loadingText: '下载中...',
      autoOpen: true,
      saveToAlbum: false,
      fileName: '',
      showSuccessToast: true,
      showErrorToast: true,
      timeout: 30000, // 30秒超时
      headers: {}
    }

    const config = { ...defaultOptions, ...options }

    try {
      // 显示加载提示
      if (config.showLoading) {
        uni.showLoading({
          title: config.loadingText,
          mask: true
        })
      }

      // 获取文件信息
      const fileInfo = this.getFileInfo(url, config.fileName)

      // 根据文件类型选择下载策略
      if (fileInfo.isImage) {
        return await this.downloadImage(url, fileInfo, config)
      } else {
        return await this.downloadDocument(url, fileInfo, config)
      }

    } catch (error) {
      console.error('文件下载失败:', error)

      if (config.showLoading) {
        uni.hideLoading()
      }

      if (config.showErrorToast) {
        uni.showToast({
          title: error.message || '下载失败',
          icon: 'none',
          duration: 2000
        })
      }

      throw error
    }
  }

  /**
   * 下载图片文件
   */
  async downloadImage(url, fileInfo, config) {
    return new Promise((resolve, reject) => {
      // 图片预览
      if (config.autoOpen && !config.saveToAlbum) {
        if (config.showLoading) {
          uni.hideLoading()
        }

        uni.previewImage({
          urls: [url],
          current: url,
          success: () => {
            if (config.showSuccessToast) {
              uni.showToast({
                title: '预览成功',
                icon: 'success'
              })
            }
            resolve({
              success: true,
              action: 'preview',
              url: url,
              fileInfo: fileInfo
            })
          },
          fail: (error) => {
            console.error('预览图片失败:', error)
            // 预览失败，尝试下载
            this.downloadImageFile(url, fileInfo, config, resolve, reject)
          }
        })
      } else {
        // 下载图片到本地
        this.downloadImageFile(url, fileInfo, config, resolve, reject)
      }
    })
  }

  /**
   * 下载图片文件到本地
   */
  downloadImageFile(url, fileInfo, config, resolve, reject) {
    uni.downloadFile({
      url: url,
      header: config.headers,
      timeout: config.timeout,
      success: (res) => {
        if (config.showLoading) {
          uni.hideLoading()
        }

        if (res.statusCode === 200) {
          // 保存到相册
          if (config.saveToAlbum) {
            uni.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                if (config.showSuccessToast) {
                  uni.showToast({
                    title: '保存成功',
                    icon: 'success'
                  })
                }
                resolve({
                  success: true,
                  action: 'save',
                  tempFilePath: res.tempFilePath,
                  fileInfo: fileInfo
                })
              },
              fail: (error) => {
                console.error('保存图片失败:', error)
                reject(new Error('保存图片失败'))
              }
            })
          } else {
            if (config.showSuccessToast) {
              uni.showToast({
                title: '下载成功',
                icon: 'success'
              })
            }
            resolve({
              success: true,
              action: 'download',
              tempFilePath: res.tempFilePath,
              fileInfo: fileInfo
            })
          }
        } else {
          reject(new Error(`下载失败，状态码: ${res.statusCode}`))
        }
      },
      fail: (error) => {
        if (config.showLoading) {
          uni.hideLoading()
        }
        console.error('下载图片失败:', error)
        reject(new Error('网络错误，下载失败'))
      }
    })
  }

  /**
   * 下载文档文件
   */
  async downloadDocument(url, fileInfo, config) {
    return new Promise((resolve, reject) => {
      uni.downloadFile({
        url: url,
        header: config.headers,
        timeout: config.timeout,
        success: (res) => {
          if (config.showLoading) {
            uni.hideLoading()
          }

          if (res.statusCode === 200) {
            if (config.autoOpen) {
              // 自动打开文档
              uni.openDocument({
                filePath: res.tempFilePath,
                fileType: fileInfo.extension.replace('.', ''),
                success: () => {
                  if (config.showSuccessToast) {
                    uni.showToast({
                      title: '打开成功',
                      icon: 'success'
                    })
                  }
                  resolve({
                    success: true,
                    action: 'open',
                    tempFilePath: res.tempFilePath,
                    fileInfo: fileInfo
                  })
                },
                fail: (error) => {
                  console.error('打开文档失败:', error)
                  // 打开失败，但下载成功
                  if (config.showErrorToast) {
                    uni.showToast({
                      title: '文件已下载，但打开失败',
                      icon: 'none',
                      duration: 3000
                    })
                  }
                  resolve({
                    success: true,
                    action: 'download',
                    tempFilePath: res.tempFilePath,
                    fileInfo: fileInfo,
                    openError: error
                  })
                }
              })
            } else {
              if (config.showSuccessToast) {
                uni.showToast({
                  title: '下载成功',
                  icon: 'success'
                })
              }
              resolve({
                success: true,
                action: 'download',
                tempFilePath: res.tempFilePath,
                fileInfo: fileInfo
              })
            }
          } else {
            reject(new Error(`下载失败，状态码: ${res.statusCode}`))
          }
        },
        fail: (error) => {
          if (config.showLoading) {
            uni.hideLoading()
          }
          console.error('下载文档失败:', error)
          reject(new Error('网络错误，下载失败'))
        }
      })
    })
  }

  /**
   * 获取文件信息
   */
  getFileInfo(url, customFileName = '') {
    const urlParts = url.split('/')
    const fileNameWithQuery = urlParts[urlParts.length - 1]
    const fileName = fileNameWithQuery.split('?')[0]

    const extension = this.getExtensionFromPath(fileName).toLowerCase()
    const name = customFileName || fileName

    // 图片类型判断
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico']
    const isImage = imageExtensions.includes(extension)

    // 文档类型判断
    const documentExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.rtf']
    const isDocument = documentExtensions.includes(extension)

    return {
      url: url,
      name: name,
      extension: extension,
      isImage: isImage,
      isDocument: isDocument,
      type: isImage ? 'image' : isDocument ? 'document' : 'unknown'
    }
  }

  /**
   * 获取平台信息文本
   */
  getPlatformInfo() {
    return {
      platform: this.platform.platform,
      isIOS: this.isIOS,
      isAndroid: this.isAndroid,
      isMiniProgram: this.isMiniProgram,
      description: this.isIOS ? 'iOS设备' :
                   this.isAndroid ? 'Android设备' :
                   this.platform.isH5 ? 'H5环境' : '未知平台'
    }
  }

  /**
   * 预览文件（兼容方法）
   * 根据文件类型自动选择预览或下载
   */
  async previewFile(url, options = {}) {
    const defaultOptions = {
      autoOpen: true,
      showLoading: true,
      loadingText: '加载中...'
    }

    const config = { ...defaultOptions, ...options }
    return await this.downloadFile(url, config)
  }

  /**
   * 批量下载文件
   */
  async downloadFiles(urls, options = {}) {
    const defaultOptions = {
      showProgress: true,
      concurrent: 3, // 并发下载数量
      showLoading: false // 批量下载时不显示单个文件的loading
    }

    const config = { ...defaultOptions, ...options }
    const results = []
    const errors = []

    if (config.showProgress) {
      uni.showLoading({
        title: `下载中 0/${urls.length}`,
        mask: true
      })
    }

    // 分批并发下载
    for (let i = 0; i < urls.length; i += config.concurrent) {
      const batch = urls.slice(i, i + config.concurrent)
      const batchPromises = batch.map(async (url, index) => {
        try {
          const result = await this.downloadFile(url, {
            ...config,
            showLoading: false,
            showSuccessToast: false,
            showErrorToast: false
          })

          if (config.showProgress) {
            uni.showLoading({
              title: `下载中 ${results.length + 1}/${urls.length}`,
              mask: true
            })
          }

          return { success: true, url, result }
        } catch (error) {
          return { success: false, url, error }
        }
      })

      const batchResults = await Promise.all(batchPromises)

      batchResults.forEach(item => {
        if (item.success) {
          results.push(item.result)
        } else {
          errors.push({ url: item.url, error: item.error })
        }
      })
    }

    if (config.showProgress) {
      uni.hideLoading()
    }

    // 显示结果提示
    if (errors.length === 0) {
      uni.showToast({
        title: `全部下载成功 (${results.length}个)`,
        icon: 'success'
      })
    } else if (results.length === 0) {
      uni.showToast({
        title: '全部下载失败',
        icon: 'error'
      })
    } else {
      uni.showToast({
        title: `下载完成: 成功${results.length}个，失败${errors.length}个`,
        icon: 'none',
        duration: 3000
      })
    }

    return {
      success: results,
      errors: errors,
      total: urls.length,
      successCount: results.length,
      errorCount: errors.length
    }
  }
}

// 创建全局实例
const fileHelper = new FileHelper()

// 导出
export default fileHelper

// 兼容CommonJS
if (typeof module !== 'undefined' && module.exports) {
  module.exports = fileHelper
}