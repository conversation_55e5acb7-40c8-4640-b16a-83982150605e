/**
 * 文件选择工具 - 针对iOS端优化
 * 解决iOS端文件选择失效问题
 * 版本: v1.0.0
 */

/**
 * 智能文件选择器
 * 根据平台和环境选择最佳的文件选择方案
 */
class FileHelper {
  constructor() {
    this.platform = this.getPlatform()
    this.isIOS = this.platform.isIOS
    this.isAndroid = this.platform.isAndroid
    this.isMiniProgram = this.platform.isMiniProgram
  }

  /**
   * 获取平台信息
   */
  getPlatform() {
    const platform = uni.getSystemInfoSync().platform
    const appPlatform = uni.getSystemInfoSync().appPlatform || ''
    
    return {
      isIOS: platform === 'ios',
      isAndroid: platform === 'android',
      isMiniProgram: typeof wx !== 'undefined',
      isH5: typeof window !== 'undefined',
      platform: platform,
      appPlatform: appPlatform
    }
  }

  /**
   * 智能文件选择
   * @param {Object} options 选择配置
   * @returns {Promise} 选择结果
   */
  async chooseFile(options = {}) {
    const defaultOptions = {
      count: 5,
      type: 'all',
      extension: [],
      showChooseDialog: true
    }
    
    const config = { ...defaultOptions, ...options }
    
    try {
      // 小程序环境
      if (this.isMiniProgram) {
        return await this.chooseMessageFile(config)
      }
      
      // H5环境
      if (this.platform.isH5) {
        return await this.chooseFileH5(config)
      }
      
      // App环境 - iOS需要特殊处理
      if (this.isIOS) {
        return await this.chooseFileIOS(config)
      }
      
      // Android默认方案
      return await this.chooseFileDefault(config)
      
    } catch (error) {
      console.error('文件选择失败:', error)
      throw error
    }
  }

  /**
   * 微信小程序文件选择 - iOS优化
   */
  async chooseMessageFile(config) {
    return new Promise((resolve, reject) => {
      // iOS端扩展名格式优化
      let extension = []
      if (config.extension && config.extension.length > 0) {
        extension = this.formatExtensionForIOS(config.extension)
      }

      // 显示选择对话框
      if (config.showChooseDialog && this.isIOS) {
        uni.showModal({
          title: '选择文件',
          content: 'iOS设备需要从微信聊天记录中选择文件',
          showCancel: true,
          confirmText: '继续',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              this.performChooseMessageFile(config, extension, resolve, reject)
            } else {
              reject(new Error('用户取消选择'))
            }
          }
        })
      } else {
        this.performChooseMessageFile(config, extension, resolve, reject)
      }
    })
  }

  /**
   * 执行微信聊天文件选择
   */
  performChooseMessageFile(config, extension, resolve, reject) {
    const chooseOptions = {
      count: config.count,
      type: config.type === 'image' ? 'image' : 
            config.type === 'video' ? 'video' : 'file',
      success: (res) => {
        if (res.tempFiles && res.tempFiles.length > 0) {
          const files = res.tempFiles.map(file => ({
            path: file.path,
            size: file.size,
            name: file.name || this.generateFileName(file.path),
            type: file.type || this.getFileTypeFromPath(file.path)
          }))
          resolve({ tempFiles: files })
        } else {
          reject(new Error('未选择任何文件'))
        }
      },
      fail: (err) => {
        console.error('chooseMessageFile失败:', err)
        // iOS端回退方案
        if (this.isIOS) {
          this.chooseImageFallback(config, resolve, reject)
        } else {
          reject(err)
        }
      }
    }

    // 添加扩展名筛选
    if (extension.length > 0) {
      chooseOptions.extension = extension
    }

    // 使用原生微信API
    if (typeof wx !== 'undefined' && wx.chooseMessageFile) {
      wx.chooseMessageFile(chooseOptions)
    } else {
      // uniapp封装的API
      uni.chooseMessageFile(chooseOptions)
    }
  }

  /**
   * iOS端扩展名格式化
   * 解决iOS端扩展名格式问题
   */
  formatExtensionForIOS(extensions) {
    const formatted = []
    
    extensions.forEach(ext => {
      // 确保扩展名格式正确
      let cleanExt = ext.trim().toLowerCase()
      
      // 添加带点和不带点的版本
      if (cleanExt.startsWith('.')) {
        formatted.push(cleanExt)        // .pdf
        formatted.push(cleanExt.slice(1)) // pdf
      } else {
        formatted.push(cleanExt)          // pdf
        formatted.push('.' + cleanExt)    // .pdf
      }
    })
    
    return [...new Set(formatted)] // 去重
  }

  /**
   * iOS专用文件选择
   */
  async chooseFileIOS(config) {
    // iOS App端暂时使用图片选择作为回退
    return new Promise((resolve, reject) => {
      uni.showActionSheet({
        itemList: ['选择图片', '选择视频'],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.chooseImageFallback(config, resolve, reject)
          } else if (res.tapIndex === 1) {
            this.chooseVideoFallback(config, resolve, reject)
          }
        },
        fail: reject
      })
    })
  }

  /**
   * 图片选择回退方案
   */
  chooseImageFallback(config, resolve, reject) {
    uni.chooseImage({
      count: config.count,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const files = res.tempFilePaths.map((path, index) => ({
          path: path,
          size: res.tempFiles ? res.tempFiles[index]?.size || 0 : 0,
          name: this.generateFileName(path),
          type: 'image'
        }))
        resolve({ tempFiles: files })
      },
      fail: reject
    })
  }

  /**
   * 视频选择回退方案
   */
  chooseVideoFallback(config, resolve, reject) {
    uni.chooseVideo({
      sourceType: ['album', 'camera'],
      maxDuration: 60,
      camera: 'back',
      success: (res) => {
        const file = {
          path: res.tempFilePath,
          size: res.size || 0,
          name: this.generateFileName(res.tempFilePath),
          type: 'video',
          duration: res.duration
        }
        resolve({ tempFiles: [file] })
      },
      fail: reject
    })
  }

  /**
   * H5环境文件选择
   */
  async chooseFileH5(config) {
    return new Promise((resolve, reject) => {
      uni.chooseFile({
        count: config.count,
        type: config.type,
        extension: config.extension,
        success: resolve,
        fail: reject
      })
    })
  }

  /**
   * 默认文件选择方案
   */
  async chooseFileDefault(config) {
    return new Promise((resolve, reject) => {
      uni.chooseFile({
        count: config.count,
        type: config.type,
        extension: config.extension,
        success: resolve,
        fail: (err) => {
          // 失败时尝试图片选择
          this.chooseImageFallback(config, resolve, reject)
        }
      })
    })
  }

  /**
   * 根据文件路径生成文件名
   */
  generateFileName(path) {
    const timestamp = Date.now()
    const extension = this.getExtensionFromPath(path)
    return `file_${timestamp}${extension}`
  }

  /**
   * 从路径获取文件扩展名
   */
  getExtensionFromPath(path) {
    const lastDot = path.lastIndexOf('.')
    return lastDot > -1 ? path.substring(lastDot) : ''
  }

  /**
   * 从路径获取文件类型
   */
  getFileTypeFromPath(path) {
    const extension = this.getExtensionFromPath(path).toLowerCase()
    
    const typeMap = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg', 
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.mp4': 'video/mp4',
      '.mov': 'video/quicktime',
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    }
    
    return typeMap[extension] || 'application/octet-stream'
  }

  /**
   * 批量上传文件
   */
  async uploadFiles(files, uploadUrl, options = {}) {
    const defaultOptions = {
      name: 'file',
      formData: {},
      header: {}
    }
    
    const config = { ...defaultOptions, ...options }
    const uploadPromises = []
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      
      const uploadPromise = new Promise((resolve, reject) => {
        uni.uploadFile({
          url: uploadUrl,
          filePath: file.path,
          name: config.name,
          formData: {
            ...config.formData,
            fileName: file.name,
            fileType: file.type
          },
          header: config.header,
          success: (res) => {
            try {
              const result = JSON.parse(res.data)
              resolve(result)
            } catch (e) {
              resolve({ code: 200, data: res.data })
            }
          },
          fail: reject
        })
      })
      
      uploadPromises.push(uploadPromise)
    }
    
    return Promise.all(uploadPromises)
  }

  /**
   * 获取平台信息文本
   */
  getPlatformInfo() {
    return {
      platform: this.platform.platform,
      isIOS: this.isIOS,
      isAndroid: this.isAndroid,
      isMiniProgram: this.isMiniProgram,
      description: this.isIOS ? 'iOS设备' : 
                   this.isAndroid ? 'Android设备' : 
                   this.platform.isH5 ? 'H5环境' : '未知平台'
    }
  }
}

// 创建全局实例
const fileHelper = new FileHelper()

// 导出
export default fileHelper

// 兼容CommonJS
if (typeof module !== 'undefined' && module.exports) {
  module.exports = fileHelper
} 