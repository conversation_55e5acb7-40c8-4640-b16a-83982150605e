<template>
  <uni-popup ref="visibleDeptPopup" type="left" class="dept-visible-popup">
    <view class="side-popup-content">
      <view class="side-popup-header">
        <text class="side-popup-title">选择可见部门</text>
        <view class="side-popup-close" @tap="close">
          <text class="close-icon">×</text>
        </view>
      </view>
      <view class="side-popup-body">
        <scroll-view scroll-y style="height: 100%;">
          <checkbox-group @change="handleVisibleDeptChange">
            <block v-for="(item, index) in deptOptions" :key="index">
              <view class="tree-node">
                <view class="tree-node-content" @tap="toggleDeptExpand(item)">
                  <view class="expand-icon" v-if="item.children && item.children.length">
                    <text>{{ isExpanded(item.id) ? '▼' : '▶' }}</text>
                  </view>
                  <view class="empty-icon" v-else></view>
                  <checkbox :value="item.id + ''" :checked="selectedDepts.includes(item.id + '')" color="#4080FF" @tap.stop />
                  <text class="dept-name">{{item.label}}</text>
                </view>
                
                <!-- 子部门，根据展开状态显示 -->
                <view class="tree-children" v-if="isExpanded(item.id) && item.children && item.children.length">
                  <view 
                    v-for="(child, childIndex) in item.children" 
                    :key="childIndex"
                    class="tree-node child-node"
                  >
                    <view class="tree-node-content" @tap="toggleDeptExpand(child)">
                      <view class="expand-icon" v-if="child.children && child.children.length">
                        <text>{{ isExpanded(child.id) ? '▼' : '▶' }}</text>
                      </view>
                      <view class="empty-icon" v-else></view>
                      <checkbox :value="child.id + ''" :checked="selectedDepts.includes(child.id + '')" color="#4080FF" @tap.stop />
                      <text class="dept-name">{{child.label}}</text>
                    </view>
                    
                    <!-- 三级部门 -->
                    <view class="tree-children" v-if="isExpanded(child.id) && child.children && child.children.length">
                      <view 
                        v-for="(subChild, subIndex) in child.children" 
                        :key="subIndex"
                        class="tree-node sub-child-node"
                      >
                        <view class="tree-node-content">
                          <view class="empty-icon"></view>
                          <checkbox :value="subChild.id + ''" :checked="selectedDepts.includes(subChild.id + '')" color="#4080FF" @tap.stop />
                          <text class="dept-name">{{subChild.label}}</text>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </block>
          </checkbox-group>
        </scroll-view>
      </view>
      <view class="side-popup-footer">
        <view class="footer-btn cancel-btn" @tap="close">取消</view>
        <view class="footer-btn confirm-btn" @tap="confirm">确定</view>
      </view>
    </view>
  </uni-popup>
</template>

<script>
import { deptTree } from '@/api/rant/respdet.js';

export default {
  name: 'DeptVisible',
  props: {
    selectedDepts: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      deptOptions: [],
      expandedDepts: [],
      localSelectedDepts: []
    }
  },
  created() {
    // 复制传入的选中部门到本地变量
    this.localSelectedDepts = [...this.selectedDepts];
  },
  watch: {
    // 当部门数据加载后自动展开第一级
    deptOptions: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.expandFirstLevel();
        }
      },
      immediate: true
    }
  },
  methods: {
    // 展开第一级部门
    expandFirstLevel() {
      if (this.deptOptions && this.deptOptions.length > 0) {
        this.deptOptions.forEach(item => {
          if (!this.expandedDepts.includes(item.id + '')) {
            this.expandedDepts.push(item.id + '');
          }
        });
      }
    },
    
    show() {
      // 确保有可见部门数据
      if (this.deptOptions.length === 0) {
        this.getDeptOptions();
      } else {
        // 确保第一级已展开
        this.expandFirstLevel();
      }
      this.$refs.visibleDeptPopup.open();
    },
    
    close() {
      this.$refs.visibleDeptPopup.close();
    },
    
    // 确认选择
    confirm() {
      // 整理所选的部门数据
      const selectedDeptInfo = this.getSelectedDeptInfo();
      // 发送选择结果到父组件
      this.$emit('feedbackEmit', selectedDeptInfo);
      this.close();
    },
    
    // 获取选中部门的完整信息
    getSelectedDeptInfo() {
      const selectedIds = [...this.localSelectedDepts];
      const selectedNames = [];
      
      // 递归查找部门名称
      const findDeptName = (depts, id) => {
        for (const dept of depts) {
          if (dept.id + '' === id + '') {
            return dept.label;
          }
          
          if (dept.children && dept.children.length > 0) {
            const name = findDeptName(dept.children, id);
            if (name) return name;
          }
        }
        return null;
      };
      
      // 获取所有选中部门的名称
      selectedIds.forEach(id => {
        const name = findDeptName(this.deptOptions, id);
        if (name) selectedNames.push(name);
      });
      
      return {
        deptIds: selectedIds,
        deptNames: selectedNames
      };
    },
    
    // 处理部门选择变化
    handleVisibleDeptChange(e) {
      this.localSelectedDepts = e.detail.value;
    },
    
    // 获取部门数据
    getDeptOptions() {
      deptTree().then(res => {
        if (res.code === 200) {
          this.deptOptions = res.data;
        }
      }).catch(err => {
        console.error('获取可见部门数据失败:', err);
        uni.showToast({
          icon: 'none',
          title: '获取可见部门数据失败'
        });
      });
    },
    
    // 判断部门是否展开
    isExpanded(id) {
      return this.expandedDepts.includes(id + '');
    },
    
    // 切换部门展开状态
    toggleDeptExpand(item) {
      const id = item.id + '';
      const index = this.expandedDepts.indexOf(id);
      if (index === -1) {
        // 不存在，添加到展开列表
        this.expandedDepts.push(id);
      } else {
        // 存在，从展开列表移除
        this.expandedDepts.splice(index, 1);
      }
    }
  }
};
</script>

<style>
.dept-visible-popup :deep(.uni-popup__wrapper) {
  width: 90vw !important;
}

.side-popup-content {
  background-color: #FFFFFF;
  border-radius: 16rpx 0 0 16rpx;
  padding: 30rpx;
  height: 100%;
  width: 90vw;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.side-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.side-popup-title {
  font-weight: 500;
  font-size: 30rpx;
  color: #333333;
}

.side-popup-close {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.close-icon {
  font-size: 28rpx;
  color: #CCCCCC;
}

.side-popup-body {
  /* flex: 1;
  overflow: hidden; */
  height: 80vh;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  flex-wrap: wrap;
}

.tree-node-content {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
  width: 100%;
  overflow: hidden;
}

.expand-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
  color: #CCCCCC;
  flex-shrink: 0;
}

.empty-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
  flex-shrink: 0;
}

.dept-name {
  font-size: 28rpx;
  color: #333333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 75vw;
}

.tree-children {
  width: 100%;
  margin-left: 20rpx;
}

.tree-node.child-node {
  margin-left: 20rpx;
}

.tree-node.sub-child-node {
  margin-left: 40rpx;
}

.side-popup-footer {
  margin-top: 30rpx;
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.footer-btn {
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  text-align: center;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  flex: 1;
}

.cancel-btn {
  background-color: #F5F5F5;
  color: #333333;
}

.confirm-btn {
  background-color: #4080FF;
  color: #FFFFFF;
}
</style> 