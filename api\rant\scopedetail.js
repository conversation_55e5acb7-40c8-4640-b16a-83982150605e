import { post, get, put, del } from '@/utils/request.uni';

// 查询可见范围详情列表
export function listScopedetail(query) {
  return get('/rant/scopedetail/list', query)
}

// 查询可见范围详情详细
export function getScopedetail(id) {
  return get('/rant/scopedetail/' + id)
}

// 新增可见范围详情
export function addScopedetail(data) {
  return post('/rant/scopedetail', data)
}

// 修改可见范围详情
export function updateScopedetail(data) {
  return put('/rant/scopedetail', data)
}

// 删除可见范围详情
export function delScopedetail(id) {
  return del('/rant/scopedetail/' + id)
}
