import { post, get, put, del } from '@/utils/request.uni';

// 查询评价信息列表
export function listEvaluative(query) {
  return get('/rant/evaluative/list', query)
}

// 查询评价信息详细
export function getEvaluative(id) {
  return get('/rant/evaluative/' + id)
}

// 新增评价信息
export function addEvaluative(data) {
  return post('/rant/evaluative', data)
}

// 修改评价信息
export function updateEvaluative(data) {
  return put('/rant/evaluative', data)
}

// 删除评价信息
export function delEvaluative(id) {
  return del('/rant/evaluative/' + id)
}

// 查询评价信息列表
export function evaluativeList(params = {}) {
  return get('/rant/evaluative/list', params)
}

// 查询评价平均分
export function avgScore(rantMattersId) {
  return get('/rant/evaluative/avgScore/' + rantMattersId)
}
