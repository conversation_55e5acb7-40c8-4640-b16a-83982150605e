/**
 * 滚动辅助工具
 * 兼容鸿蒙系统的滚动解决方案
 */

// 检测当前运行环境
export function detectPlatform() {
  const systemInfo = uni.getSystemInfoSync();
  return {
    isHarmonyOS: systemInfo.platform === 'harmonyos' || 
                 systemInfo.system.toLowerCase().includes('harmonyos') ||
                 systemInfo.brand.toLowerCase().includes('huawei'),
    platform: systemInfo.platform,
    system: systemInfo.system
  };
}

/**
 * 兼容性滚动到页面底部
 * @param {Object} options 配置选项
 * @param {number} options.duration 动画持续时间，默认300ms
 * @param {Function} options.success 成功回调
 * @param {Function} options.fail 失败回调
 */
export function scrollToBottom(options = {}) {
  const { duration = 300, success, fail } = options;
  const platform = detectPlatform();
  
  // 方案1: 使用 uni.pageScrollTo (标准方案)
  const useUniScrollTo = () => {
    return new Promise((resolve, reject) => {
      uni.pageScrollTo({
        scrollTop: 99999,
        duration,
        success: () => {
          success && success();
          resolve();
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  };
  
  // 方案2: 使用 createSelectorQuery 获取页面高度后滚动
  const useQueryScrollTo = () => {
    return new Promise((resolve, reject) => {
      const query = uni.createSelectorQuery();
      query.select('body').boundingClientRect();
      query.selectViewport().scrollOffset();
      query.exec((res) => {
        try {
          let scrollTop = 0;
          if (res[0] && res[0].height) {
            scrollTop = res[0].height;
          } else if (res[1] && res[1].scrollHeight) {
            scrollTop = res[1].scrollHeight;
          } else {
            // 使用系统信息计算
            const systemInfo = uni.getSystemInfoSync();
            scrollTop = systemInfo.screenHeight * 3; // 保守估计
          }
          
          uni.pageScrollTo({
            scrollTop,
            duration,
            success: () => {
              success && success();
              resolve();
            },
            fail: reject
          });
        } catch (error) {
          reject(error);
        }
      });
    });
  };
  
  // 方案3: 使用 DOM 操作 (H5专用)
  const useDOMScroll = () => {
    return new Promise((resolve, reject) => {
      // #ifdef H5
      try {
        const element = document.documentElement || document.body;
        const targetScrollTop = element.scrollHeight;
        
        if (duration <= 0) {
          element.scrollTop = targetScrollTop;
          success && success();
          resolve();
          return;
        }
        
        const startScrollTop = element.scrollTop;
        const startTime = Date.now();
        
        const scroll = () => {
          const now = Date.now();
          const elapsed = now - startTime;
          const progress = Math.min(elapsed / duration, 1);
          
          // 使用缓动函数
          const easeProgress = 1 - Math.pow(1 - progress, 3);
          element.scrollTop = startScrollTop + (targetScrollTop - startScrollTop) * easeProgress;
          
          if (progress < 1) {
            requestAnimationFrame(scroll);
          } else {
            success && success();
            resolve();
          }
        };
        
        requestAnimationFrame(scroll);
      } catch (error) {
        reject(error);
      }
      // #endif
      
      // #ifndef H5
      reject(new Error('DOM scroll not supported'));
      // #endif
    });
  };
  
  // 按优先级尝试不同方案
  const tryMethods = async () => {
    const methods = [];
    
    // 对于鸿蒙系统，优先使用查询方案
    if (platform.isHarmonyOS) {
      methods.push(useQueryScrollTo, useUniScrollTo);
    } else {
      methods.push(useUniScrollTo, useQueryScrollTo);
    }
    
    // H5环境添加DOM方案
    // #ifdef H5
    methods.push(useDOMScroll);
    // #endif
    
    for (const method of methods) {
      try {
        await method();
        return;
      } catch (error) {
        console.warn('滚动方法失败:', error);
        continue;
      }
    }
    
    // 所有方法都失败了
    fail && fail(new Error('所有滚动方法都失败'));
  };
  
  tryMethods();
}

/**
 * 兼容性滚动到页面顶部
 * @param {Object} options 配置选项
 */
export function scrollToTop(options = {}) {
  const { duration = 300, success, fail } = options;
  
  uni.pageScrollTo({
    scrollTop: 0,
    duration,
    success,
    fail
  });
}

/**
 * 兼容性滚动到指定位置
 * @param {Object} options 配置选项
 * @param {number} options.scrollTop 滚动位置
 * @param {number} options.duration 动画持续时间
 * @param {Function} options.success 成功回调
 * @param {Function} options.fail 失败回调
 */
export function scrollTo(options = {}) {
  const { scrollTop = 0, duration = 300, success, fail } = options;
  
  uni.pageScrollTo({
    scrollTop,
    duration,
    success,
    fail
  });
}

/**
 * 平滑滚动到指定元素
 * @param {string} selector 元素选择器
 * @param {Object} options 配置选项
 */
export function scrollToElement(selector, options = {}) {
  const { duration = 300, offset = 0, success, fail, maxRetry = 3 } = options;
  
  let retryCount = 0;
  
  const attemptScroll = () => {
    const query = uni.createSelectorQuery();
    query.select(selector).boundingClientRect();
    query.selectViewport().scrollOffset(); // 获取当前滚动位置
    query.exec((res) => {
      const elementData = res[0];
      const scrollData = res[1];
      
      if (elementData && elementData.top !== undefined) {
        // 计算目标滚动位置 = 当前滚动位置 + 元素距离视口顶部的距离 + 偏移量
        const targetScrollTop = (scrollData?.scrollTop || 0) + elementData.top + offset;
        
        uni.pageScrollTo({
          scrollTop: Math.max(0, targetScrollTop),
          duration,
          success: () => {
            console.log(`滚动到元素 ${selector} 成功，目标位置: ${targetScrollTop}`);
            success && success();
          },
          fail: (error) => {
            console.warn(`滚动到元素 ${selector} 失败:`, error);
            fail && fail(error);
          }
        });
      } else {
        retryCount++;
        if (retryCount < maxRetry) {
          console.warn(`元素 ${selector} 未找到，重试第 ${retryCount} 次`);
          setTimeout(attemptScroll, 100); // 100ms后重试
        } else {
          const error = new Error(`Element not found after ${maxRetry} retries: ${selector}`);
          console.error(error.message);
          fail && fail(error);
        }
      }
    });
  };
  
  attemptScroll();
}

/**
 * 智能的焦点滚动，专为输入框焦点设计
 * 会自动处理DOM更新、键盘弹起等情况
 * @param {Function} triggerDOMUpdate 触发DOM更新的函数
 * @param {Object} options 配置选项
 */
export function smartFocusScroll(triggerDOMUpdate, options = {}) {
  const { 
    targetSelector = '.scroll-target',
    duration = 300, 
    offset = -50,
    waitTime = 150,
    success, 
    fail 
  } = options;
  
  // 执行DOM更新
  if (typeof triggerDOMUpdate === 'function') {
    triggerDOMUpdate();
  }
  
  // 等待DOM更新完成后滚动
  setTimeout(() => {
    scrollToElement(targetSelector, {
      duration,
      offset,
      success: () => {
        console.log('智能焦点滚动成功');
        success && success();
      },
      fail: (error) => {
        console.warn('智能焦点滚动失败，使用备用方案:', error);
        // 备用方案：滚动到页面底部
        scrollToBottom({
          duration,
          success: () => {
            console.log('备用滚动方案成功');
            success && success();
          },
          fail: (backupError) => {
            console.error('所有滚动方案都失败:', backupError);
            fail && fail(backupError);
          }
        });
      }
    });
  }, waitTime);
}

export default {
  detectPlatform,
  scrollToBottom,
  scrollToTop,
  scrollTo,
  scrollToElement,
  smartFocusScroll
}; 