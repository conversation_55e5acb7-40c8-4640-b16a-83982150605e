<template>
  <view :class="['task-container', { 'popup-open': isPopupOpen }]">
    <!-- Filter Area -->
    <view class="filter-area">
      <view class="filter-item" @tap="openTypePopup">
        <text>{{ getSelectedTypesLabel() || '类型' }}</text>
        <uni-icons type="bottom" :size="12"></uni-icons>
      </view>
      <view class="filter-item" @tap="openStatusPicker">
        <text>{{ getStatusLabel(queryParams.status) || '状态' }}</text>
        <uni-icons type="bottom" size=""></uni-icons>
      </view>
      <view class="search-box">
        <!-- <text class="icon-search">🔍</text> -->
        <uni-icons class="icon-search" type="search" size="20"></uni-icons>
        <input class="search-input" type="text" v-model="queryParams.rantContent" placeholder="搜索内容"
          confirm-type="search" @input="handleSearchInput" @confirm="handleSearch" />
        <text class="clear-icon" v-if="queryParams.rantContent" @tap="clearSearch">×</text>
      </view>
    </view>

    <!-- Task List -->
    <scroll-view scroll-y class="task-scroll-view" refresher-enabled :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh" @scrolltolower="loadMoreData">
      <view class="task-list">
        <view v-for="task in taskList" :key="task.id" class="task-card" @tap="navigateToFeedback(task.id)">
          <view class="task-header">
            <!-- 督办类型 -->
            <text class="task-title">{{ getTaskTypeLabel(task.mattersType) || task.title }}</text>
            <!-- 事项状态 -->
            <view :class="['task-status', getStatusClass(task.status)]">{{ getStatusText(task.status) }}</view>
          </view>
          <view class="task-subheader">
            <view class="person-type">
              <!-- 来源 -->
              <!-- <text class="task-person">{{task.ranterName}}</text> -->
              <!-- 责任人 -->
              <text class="task-person">{{ task.responsiblePersonName || '--' }}</text>

              <!-- <text class="task-person">{{task.responsiblePersonName || task.person}}</text> -->
              <!-- <text class="task-type">{{task.rantClassify || task.type}}</text> -->
            </view>
            <!-- 分类 -->
            <text class="task-type">{{ task.rantClassify }}</text>
          </view>
          <!-- 督办内容 -->
          <view class="task-content">
            <text>{{ task.rantContent }}</text>
          </view>
          <!-- 如果督办事项状态为按期完成或者延期完成，就显示第四行 左边是结项时间标题，右边是结项日期 -->
          <view v-if="task.status == 2 || task.status == 3" class="task-content cu-flex-between">
            <text>结项时间</text>
            <text>{{ task.closingTime || "--" }}</text>
          </view>
          <view class="item-actions">
            <view v-if="task.status == 2 || task.status == 3" class="action-btn edit-btn"
              @tap.stop="handleEvaluation(task)">评价
            </view>
          </view>
        </view>

        <!-- Loading & Empty States -->
        <view class="loading-state" v-if="loading">
          <text>加载中...</text>
        </view>
        <view class="empty-state" v-if="!loading && taskList.length === 0">
          <text>暂无数据</text>
        </view>
        <view class="no-more" v-if="!hasMore && taskList.length > 0">
          <text>没有更多数据了</text>
        </view>
      </view>
    </scroll-view>
    <!-- Status Popup -->
    <!-- 选择类型弹窗 -->
    <CategoryPicker ref="categoryPopupMattersType" :dictType="'rant_matters_type'" :multiple="true"
      @confirmWithDetail="applyTypeSelection" />
    <StatusPicker ref="statusPopup" title="选择状态" @confirm="applyStatusSelection" :multiple="true"/>

    <!-- 评价弹窗 -->
    <uni-popup ref="evaluationPopup" type="center">
      <view :class="['evaluation-popup', { 'keyboard-active': isTextareaFocused }]">
        <view class="popup-header">
          <text class="popup-title">事项评价</text>
          <text class="close-icon" @tap="closeEvaluationPopup">×</text>
        </view>
        <view class="rating-section">
          <text class="rating-label"><text class="required">*</text>评分</text>
          <!-- <view class="star-rating">
              <uni-icons
                v-for="(star, index) in 5"
                :key="index"
                :type="evaluationScore > index ? 'star-filled' : 'star'"
                size="24"
                color="#FFCC33"
                @click="setRating(index + 1)"
              ></uni-icons>
            </view> -->
          <!-- <uni-rate allow-half :value="evaluationScore" :is-fill="false" margin="8" size="28" @change="setRating"/> -->
          <!--评分组件-->
          <RatingStars :value="evaluationScore" :max="5" @set="setRating" />
          <text class="rating-value">{{ evaluationScore * 2 }}.0分</text>
        </view>
        <view class="evaluation-content">
          <text class="content-label"><text class="required">*</text> 评价内容</text>
          <textarea class="evaluation-textarea" v-model="evaluationForm.evaluationContent" placeholder="请输入评价内容"
            maxlength="200" @focus="handleTextareaFocus" @blur="handleTextareaBlur"></textarea>
          <view class="char-count">{{ evaluationForm.evaluationContent.length }}/200</view>
        </view>
        <view class="popup-footer">
          <button class="cancel-btn" @tap="closeEvaluationPopup">取 消</button>
          <button class="submit-btn" v-if="envalatingLoading">
            提交中...
          </button>
          <button v-else class="submit-btn" @tap="submitEvaluation">
            提 交
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { getMatters, lookList, addMatters, updateMatters, delMatters } from '@/api/rant/matters'
import { addEvaluative } from '@/api/rant/evaluative'
import { getDicts } from '@/api/common'
import { rantStatusOption } from '@/constant/index'
import StatusPicker from '@/components/StatusPicker/index.vue'
import CategoryPicker from '@/components/CategoryPicker/index.vue';
import mixin from "../../mixins/mixin";
import RatingStars from '@/components/RatingStars/index.vue'
export default {
  mixins: [mixin],
  components: {
    StatusPicker,
    CategoryPicker,
    RatingStars
  },
  data() {
    return {
      taskList: [],
      types: [
        { dictValue: '', dictLabel: '全部' }
      ],
      categories: [
        { value: '', label: '全部' }
      ],
      statuses: [
        { value: 'all', label: '全部' },
        { value: '0', label: '草稿' },
        { value: '1', label: '进行中' },
        { value: '2', label: '按时完成' },
        { value: '3', label: '延期完成' },
        { value: '4', label: '延期未完成' },
        { value: '5', label: '终止' },
        { value: '6', label: '审批中' },
        { value: '7', label: '驳回' }
      ],
      statusOptions: rantStatusOption,
      selectedTypes: [],
      selectedCategory: '全部',
      selectedStatus: '全部',
      sortOrder: '',
      loading: false,
      searchTimeout: null, // 搜索防抖计时器
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mattersType: [],
        rantClassify: '',
        responsiblePerson: '',
        status: '',
        planTime: '',
        closingTime: '',
        rantContent: '',
      },
      // 评价表单
      evaluationForm: {
        rantMattersId: '',
        score: 0,
        evaluationContent: ''
      },
      evaluationVisible: false,
      evaluationScore: 0,
      hasMore: true,
      envalatingLoading: false,
      isTextareaFocused: false, // 控制textarea焦点状态
      isPopupOpen: false, // 控制弹窗打开状态，用于禁止背景滚动
      isRefreshing: false, // 控制下拉刷新状态
    }
  },
  onLoad() {
    // 加载初始数据
    this.fetchTypes();
    // this.fetchCategories();
  },
  onShow() {
    this.fetchTaskList();
  },

  methods: {
    /** 评价按钮操作 */
    handleEvaluation(row) {
      this.evaluationForm = {
        rantMattersId: row.id,
        score: 0,
        evaluationContent: ''
      }
      this.evaluationScore = 0;
      // this.evaluationScore = row.evaluationScore
      this.isPopupOpen = true; // 设置弹窗打开状态，禁止背景滚动
      this.$refs.evaluationPopup.open()
    },

    /** 关闭评价弹窗 */
    closeEvaluationPopup() {
      this.isPopupOpen = false; // 恢复背景滚动
      this.$refs.evaluationPopup.close()
    },

    /** 设置评分 */
    setRating(val) {
      console.log('setRating:', val)
      this.evaluationScore = val
    },

    /** 提交评价 */
    async submitEvaluation() {
      // 防止重复提交
      if (this.envalatingLoading) return;

      if (!this.evaluationScore) {
        uni.showToast({
          icon: 'none',
          title: '请选择评分'
        })
        return
      }

      if (!this.evaluationForm.evaluationContent) {
        uni.showToast({
          icon: 'none',
          title: '请输入评价内容'
        })
        return
      }

      this.envalatingLoading = true;
      this.evaluationForm.score = this.evaluationScore * 2;

      try {
        uni.showLoading({
          title: '提交中...'
        });

        const res = await addEvaluative(this.evaluationForm)
        uni.hideLoading();

        if (res.code === 200) {
          uni.showToast({
            title: '评价成功',
            duration: 1500
          })
          this.isPopupOpen = false; // 恢复背景滚动
          this.$refs.evaluationPopup.close()
          this.fetchTaskList() // 刷新列表
        } else {
          uni.showToast({
            icon: 'none',
            title: res.msg || '评价失败',
            duration: 1500
          })
        }
      } catch (error) {
        console.error('提交评价失败:', error)
        uni.showToast({
          icon: 'none',
          title: '提交评价失败',
          duration: 1500
        })
      } finally {
        setTimeout(() => {
          this.envalatingLoading = false;
        }, 3500);
        // this.envalatingLoading = false;
        // uni.hideLoading();
      }
    },

    /** 下拉刷新 */
    onRefresh() {
      this.isRefreshing = true;
      this.queryParams.pageNum = 1;
      this.hasMore = true;
      this.taskList = [];
      this.fetchTaskList();
    },

    /** 上拉加载更多 */
    loadMoreData() {
      if (this.hasMore && !this.loading) {
        this.queryParams.pageNum++;
        this.fetchTaskList();
      }
    },

    /** 重置分页参数和列表 */
    resetPagination() {
      this.queryParams.pageNum = 1;
      this.hasMore = true;
      this.taskList = [];
    },

    navigateToFeedback(taskId) {
      uni.navigateTo({
        url: `/pages/look/detail?id=${taskId}&type=look`
      });
    },

    // 获取类型选项
    async fetchTypes() {
      try {
        // 使用getDicts API获取字典数据
        const res = await getDicts('rant_matters_type');
        if (res.code === 200 && res.data) {
          // 添加"全部"选项
          this.types = [{ dictValue: '', dictLabel: '全部' }, ...res.data.map(item => ({
            dictValue: item.dictValue,
            dictLabel: item.dictLabel
          }))];
        }
      } catch (error) {
        console.error('获取类型选项失败:', error);
        uni.showToast({
          icon: 'none',
          title: '获取类型选项失败'
        });
      }
    },

    // 获取分类选项
    async fetchCategories() {
      try {
        // 使用getDicts API获取字典数据
        const res = await getDicts('rant_classify');
        if (res.code === 200 && res.data) {
          // 添加"全部"选项
          this.categories = [{ value: '', label: '全部' }, ...res.data];
        }
      } catch (error) {
        console.error('获取分类选项失败:', error);
        uni.showToast({
          icon: 'none',
          title: '获取分类选项失败'
        });
      }
    },

    // Fetch data from API
    async fetchTaskList() {
      this.loading = true;
      try {
        // 创建查询参数，过滤掉空值
        const params = {};
        // 只添加非空的查询参数
        Object.keys(this.queryParams).forEach(key => {
          if (this.queryParams[key] !== null &&
            this.queryParams[key] !== undefined &&
            this.queryParams[key] !== '' &&
            !(Array.isArray(this.queryParams[key]) && this.queryParams[key].length === 0)) {
            params[key] = this.queryParams[key];
          }
        });

        console.log('params:', params)
        try {
          const res = await lookList(params);
          console.log('lookList response:', res);

          if (res.code === 200) {
            let taskList = res.rows || res.data || [];

            // Apply sorting if needed
            /* if (this.sortOrder) {
               taskList = this.sortTasks(taskList, this.sortOrder);
             }*/
            if (this.queryParams.pageNum === 1) {
              this.taskList = taskList;
            }
            else {
              this.taskList = this.taskList.concat(taskList);
            }
            this.hasMore = this.taskList.length < res.total;
          } else {
            throw new Error('API returned error code');
          }
        } catch (apiError) {
          this.taskList = [];
        }
      } catch (error) {
        uni.showToast({
          title: '获取任务列表失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        this.isRefreshing = false;
      }
    },

    // 选择分类
    selectCategory(label, value) {
      this.selectedCategory = label;
      this.queryParams.rantClassify = value;
      this.closeCategoryPopup();
      this.fetchTaskList();
    },

    // Dropdown actions
    openTypePopup() {
      this.$refs.categoryPopupMattersType.show();
    },

    closeTypePopup() {
      // this.$refs.categoryPopupMattersType.close();
    },

    openCategoryPopup() {
      this.$refs.categoryPopup.open();
    },

    closeCategoryPopup() {
      this.$refs.categoryPopup.close();
    },

    openStatusPicker() {
      this.$refs.statusPopup.show()
    },

    closeStatusPicker() {
      // this.$refs.statusPopup.close();
    },

    openMorePopup() {
      this.$refs.morePopup.open();
    },

    closeMorePopup() {
      this.$refs.morePopup.close();
    },

    sortByDate(order) {
      this.sortOrder = order;
      this.closeMorePopup();
      this.fetchTaskList();
    },

    sortTasks(tasks, order) {
      return [...tasks].sort((a, b) => {
        const dateA = a.planTime || a.date;
        const dateB = b.planTime || b.date;

        if (order === 'asc') {
          return new Date(dateA) - new Date(dateB);
        } else {
          return new Date(dateB) - new Date(dateA);
        }
      });
    },

    // Navigation
    goToSearch() {
      uni.navigateTo({
        url: '/pages/look/search'
      });
    },

    getStatusText(status) {
      const statusValue = Number(status);
      const statusObj = rantStatusOption.find(s => s.value === statusValue);
      return statusObj ? statusObj.label : status;
    },

    getSelectedTypesLabel() {
      if (!this.selectedTypes || this.selectedTypes.length === 0) {
        return '类型';
      }

      const typeLabels = this.selectedTypes.map(typeValue => {
        const type = this.types.find(t => t.dictValue === typeValue);
        return type ? type.dictLabel : '';
      }).filter(label => label);

      if (typeLabels.length === 0) {
        return '类型';
      }

      if (typeLabels.length > 1) {
        return `已选${typeLabels.length}项`;
      }

      return typeLabels[0];
    },

    getTaskTypeLabel(value) {
      if (!value) return '';

      // If value is a comma-separated string (from API), split it into an array
      const typeValues = typeof value === 'string' ? value.split(',') :
        Array.isArray(value) ? value : [value];

      // Map each value to its label
      const typeLabels = typeValues.map(val => {
        const type = this.types.find(t => t.dictValue === val);
        return type ? type.dictLabel : '';
      }).filter(label => label); // Remove empty labels

      return typeLabels.join('|');
    },

    toggleTypeSelection(value) {
      if (this.selectedTypes.includes(value)) {
        this.selectedTypes = this.selectedTypes.filter(v => v !== value);
      } else {
        this.selectedTypes.push(value);
      }
      console.log('selectedTypes:', this.selectedTypes);
    },

    resetTypeSelection() {
      this.selectedTypes = [];
    },

    applyTypeSelection(selectedTypes) {
      console.log('applyTypeSelection---------', selectedTypes)
      this.queryParams.mattersType = selectedTypes.values;
      console.log('this.queryParams.mattersType:', this.queryParams.mattersType)
      this.closeTypePopup();
      // 重置分页参数
      this.resetPagination();
      this.fetchTaskList();
    },

    // 处理状态选择变更
    handleStatusChange(e) {
      this.queryParams.status = e.value
    },

    // 处理状态选择确认
    handleStatusConfirm(e) {
      this.queryParams.status = e.value;
      // 重置分页参数
      this.resetPagination();
      this.fetchTaskList();
    },

    // 重置状态选择
    resetStatusSelection() {
      this.queryParams.status = '';
    },

    // 应用状态选择
    applyStatusSelection(status) {
      // this.closeStatusPopup();
      console.log('applyStatusSelection---------', status)
      this.queryParams.status = status.value;
      // 重置分页参数
      this.resetPagination();
      this.fetchTaskList();
    },

    getStatusLabel(value) {
      if (!value && value !== 0) return '状态';
      const status = this.statuses.find(s => String(s.value) === String(value) || (s.value === 'all' && !value));
      return status ? status.label : '状态';
    },

    // 处理搜索输入（带防抖）
    handleSearchInput() {
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout);
      }

      this.searchTimeout = setTimeout(() => {
        // 重置分页参数
        this.resetPagination();
        this.fetchTaskList();
      }, 500); // 500ms防抖
    },

    // 处理搜索
    handleSearch() {
      // 重置分页参数
      this.resetPagination();
      this.fetchTaskList();
    },

    // 清除搜索
    clearSearch() {
      this.queryParams.rantContent = '';
      // 重置分页参数
      this.resetPagination();
      this.fetchTaskList();
    },

    // 处理类型选择变更
    handleTypeChange(e) {
      const selectedValues = e.detail.value;
      const allOption = this.types.find(t => t.dictValue === '');
      const normalTypes = this.types.filter(t => t.dictValue !== '');

      // 检查是否包含"全部"选项
      const hasAll = selectedValues.includes('');

      if (hasAll) {
        // 如果选择了"全部"，则选中所有选项（包括"全部"）
        this.selectedTypes = ['', ...normalTypes.map(type => type.dictValue)];
      } else {
        // 如果没有选择"全部"，只使用选中的具体选项
        this.selectedTypes = selectedValues;

        // 检查是否手动选中了所有具体选项
        const allNormalTypesSelected = normalTypes.every(type =>
          selectedValues.includes(type.dictValue)
        );

        if (allNormalTypesSelected) {
          // 如果选中了所有具体选项，自动选中"全部"
          this.selectedTypes = ['', ...selectedValues];
        }
      }
    },

    handleTextareaFocus() {
      // textarea获得焦点时，设置状态为true
      this.isTextareaFocused = true;
    },

    handleTextareaBlur() {
      // textarea失去焦点时，设置状态为false
      this.isTextareaFocused = false;
    },
  }
}
</script>

<style lang="scss" scoped>
.task-container {
  background-color: #D6E1F1;
  min-height: 100vh;
}

/* 弹窗打开时禁止背景滚动 */
.popup-open {
  overflow: hidden !important;
  height: 100vh !important;
}

.keyboard-active {
  margin-bottom: 50vh;
}

.debug-info {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 10rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
}

.debug-info text {
  display: block;
  font-size: 20rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.page-header {
  padding: 20rpx 0;
}

.page-title {
  font-weight: 500;
  font-size: 34rpx;
  color: #333333;
}

.filter-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #D6E1F1;
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.filter-item {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFFFFF;
  border-radius: 32rpx;
  padding: 10rpx 30rpx;
  margin-right: 16rpx;
  font-size: 28rpx;
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #F5F5F5;
  border-radius: 30rpx;
  padding: 0 20rpx;
  margin-left: 10rpx;
  height: 70rpx;
}

.task-scroll-view {
  position: fixed;
  top: 100rpx;
  /* filter-area的高度 */
  left: 0;
  right: 0;
  bottom: 0;
}

.task-list {
  padding: 20rpx 24rpx 0 24rpx;
}

.filter-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}


.arrow-down {
  margin-left: 8rpx;
  font-size: 24rpx;
  color: #999;
}


.icon-search {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
}

.clear-icon {
  font-size: 32rpx;
  color: #999;
  padding: 0 10rpx;
}

.loading-state,
.empty-state {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

.popup-content {
  background-color: #FFFFFF;
  border-radius: 16rpx 16rpx 0 0;
  padding: 30rpx;
}

.popup-title {
  font-weight: 500;
  font-size: 30rpx;
  color: #333333;
  text-align: center;
  margin-bottom: 30rpx;
}

.popup-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.radio-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
  font-size: 28rpx;
  color: #333333;
}

.popup-footer {
  margin-top: 30rpx;
  display: flex;
  justify-content: space-between;
}

.popup-btn {
  width: 30%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #F5F5F5;
  color: #333333;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.popup-btn-cancel {
  background-color: #F5F5F5;
}

.popup-btn-reset {
  background-color: #F5F5F5;
}

.popup-btn-confirm {
  background-color: #4080FF;
  color: #FFFFFF;
}

/* 评价弹窗样式 */
.evaluation-popup {
  width: 650rpx;
  background-color: #fff;
  border-radius: 10rpx;
  overflow: hidden;
}

.popup-header {
  position: relative;
  padding: 30rpx;
  text-align: center;
  border-bottom: 1px solid #eee;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
}

.close-icon {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  font-size: 40rpx;
  color: #999;
}

.rating-section {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.rating-label {
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.star-rating {
  display: flex;
  gap: 20rpx;
  margin-bottom: 10rpx;
}

.rating-value {
  font-size: 28rpx;
  color: #666;
  margin-top: 10rpx;
}

.evaluation-content {
  padding: 0 30rpx 30rpx;
  position: relative;
}

.content-label {
  display: block;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.required {
  color: #f56c6c;
  margin-right: 5rpx;
}

.evaluation-textarea {
  width: 100%;
  height: 200rpx;
  border: 1px solid #dcdfe6;
  border-radius: 4rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.char-count {
  position: absolute;
  right: 40rpx;
  bottom: 40rpx;
  font-size: 24rpx;
  color: #999;
}

.popup-footer {
  display: flex;
  border-top: 1px solid #eee;
}

.cancel-btn,
.submit-btn {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 30rpx;
  border: none;
  border-radius: 0;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #333;
}

.submit-btn {
  background-color: #1890ff;
  color: #fff;
}

.submit-btn:disabled {
  background-color: #d9d9d9;
  color: #999;
  cursor: not-allowed;
}

.item-actions {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  padding: 6rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  margin-left: 16rpx;
  width: fit-content;
}

.edit-btn {
  background-color: #4080FF;
  color: white;
}

.loading-more,
.no-more,
.empty-list {
  text-align: center;
  padding: 30rpx 0;
  color: #999999;
  font-size: 24rpx;
}
</style>
