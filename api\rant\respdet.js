import { post, get, put, del } from '@/utils/request.uni';

// 查询责任人责任部门分管领导关系列表
export function listRespdet(query) {
  return get('/rant/respdet/list', query)
}

// 查询责任人责任部门分管领导关系详细
export function getRespdet(id) {
  return get('/rant/respdet/' + id)
}

// 新增责任人责任部门分管领导关系
export function addRespdet(data) {
  return post('/rant/respdet', data)
}

// 修改责任人责任部门分管领导关系
export function updateRespdet(data) {
  return put('/rant/respdet', data)
}

// 删除责任人责任部门分管领导关系
export function delRespdet(id) {
  return del('/rant/respdet/' + id)
}

// 获取部门下拉树列表
export function deptTree(params) {
  // return get('/weekly/leader/dept/tree', params)
  return get('/rant/common/dept/tree', params)
}