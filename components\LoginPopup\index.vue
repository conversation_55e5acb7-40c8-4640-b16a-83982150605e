<!-- LoginPopup.vue -->
<template>
<!--  <uni-popup ref="popupLogin" type="bottom" borderRadius="20px 20px 0 0" >
    <view class="login-block">
      <view class="content">
        欢迎您登录智地智汇数集团日报小程序！
      </view>
      <view class="btn">
        <button class="mini-btn" type="default" size="mini" @click="closePopup">取消</button>
        <button class="mini-btn" type="primary" size="mini" @click="login">登录</button>
      </view>
      
    </view>
  </uni-popup>-->
</template>

<script>
import {handleLogin} from "@/utils/utils.js";
export default {
  props: {
    visible: {
        type: Boolean,
        default: false
    }
  },
  methods: {
    openPopup(){
        this.$refs.popupLogin.open('bottom');
    },
    login() {
      handleLogin();
      // 这里可以添加登录逻辑，如调用登录接口
    },
    closePopup() {
        this.$refs.popupLogin.close();
        this.$emit('close');
    }
  }
};
</script>

<style scoped lang="scss">
  .login-block{
      padding: 80rpx;
      background-color: #fff;
  }
  .btn{
      display: flex;
      justify-content: space-between;
  }
  .content{
      padding: 40rpx;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      color: #333333;
      line-height: 40rpx;
      text-align: left;
      font-style: normal;
  }
</style>
