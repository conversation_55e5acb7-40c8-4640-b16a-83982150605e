import Vue from 'vue';
import Vuex from 'vuex';
import { post } from '@/utils/request.uni.js';
Vue.use(Vuex);

const store = new Vuex.Store({
    state: {
        access_token: uni.getStorageSync('access_token'), // 登录token
        userInfo: uni.getStorageSync('userInfo'), // 用户信息
        permissions: []
    },
    mutations: {
        setToken(state, data) {
            state.access_token = data;
        },
        setUserInfo(state, data) {
            state.userInfo = data;
        },
        setPermissions(state, data) {
            state.permissions = data;
        }
    },
    actions: {

    }
});

export default store;
