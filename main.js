import App from './App'
import "@/static/css/index.css"
import {goPage, emptyFilter} from "./utils/utils";
import scrollHelper from "./utils/scrollHelper";
import store from './store'
// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
import Layout from '@/components/Layout/index.vue';
import fileHelper from '@/utils/fileHelper'

Vue.component('Layout', Layout);
// import directives from '@/directive'
// Vue.use(directives)

// 全局注册uni-popup组件
import uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue'
import uniPopupDialog from '@/uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.vue'
import uniRate from '@/uni_modules/uni-rate/components/uni-rate/uni-rate.vue'
Vue.component('uni-popup', uniPopup)
Vue.component('uni-popup-dialog', uniPopupDialog)
Vue.component('uni-rate', uniRate)
Vue.prototype.$fileHelper = fileHelper

Vue.config.productionTip = false
Vue.prototype.$goPage = goPage;
Vue.prototype.$emptyFilter = emptyFilter;
Vue.prototype.$scrollHelper = scrollHelper;
App.mpType = 'app'
const app = new Vue({
  store,
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif
