<template>
    <view :class="['create-rant-container', { 'keyboard-activ-create': isContentFocused }]">
    <!-- 步骤指示器 -->
    <view class="step-indicator">
      <view class="step-container">
        <view class="step-line-bg"></view>
        <view class="step-items">
          <view class="step-item step-current">
            <view class="step-circle">
              <image class="step-icon-image" src="@/static/images/step-current.png"></image>
            </view>
            <text class="step-text">新增</text>
          </view>
          <view class="step-item step-wait">
            <view class="step-circle">
              <image class="step-icon-image" src="@/static/images/step-wait.png"></image>
            </view>
            <text class="step-text">初审</text>
          </view>
          <view class="step-item step-wait">
            <view class="step-circle">
              <image class="step-icon-image" src="@/static/images/step-wait.png"></image>
            </view>
            <text class="step-text">责任人确认</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 表单卡片 -->
    <view class="form-card">
      <view class="form-header">
        <text class="form-title">吐槽人</text>
        <text class="form-action">{{form.ranterName}}</text>
      </view>
      <view class="form-item">
        <text class="form-label">
          <text class="required-mark">*</text>
          吐槽分类
        </text>
        <view class="form-value" @tap="showCategoryPicker">
          <text class="form-placeholder">{{form.rantClassify || '请选择'}}</text>
          <uni-icons type="right" size="16" color="#999999"></uni-icons>
        </view>
      </view>
      
      <view class="form-item disabled">
        <text class="form-label">计划完成时间</text>
        <picker
          mode="date"
          :value="form.planTime"
          @change="handleChange"
          fields="day"
          class="date-picker-view"
          disabled
        >
          <view class="form-placeholder">
            <text>{{form.planTime || '--'}}</text>
            <!-- <uni-icons type="right" size="16" color="#999999"></uni-icons> -->
          </view>
        </picker>
      </view>
      
      <view class="form-item disabled">
        <text class="form-label">责任人</text>
<!--        @tap="showResponsiblePicker"-->
        <view class="form-value">
          <text class="form-placeholder">{{form.responsiblePersonName || '--'}}</text>
          <!-- <uni-icons type="right" size="16" color="#999999"></uni-icons> -->
        </view>
      </view>
      
      <view class="form-item disabled">
        <text class="form-label">责任部门</text>
<!--        @tap="showDepartmentPicker"-->
        <view class="form-value">
          <text class="form-placeholder">{{form.deptName || '--'}}</text>
          <!-- <uni-icons type="right" size="16" color="#999999"></uni-icons> -->
        </view>
      </view>

      <view class="form-item disabled">
        <text class="form-label">部门负责人</text>
        <view class="form-value">
          <text class="form-placeholder">{{form.respDeptResponsiblerName || '--'}}</text>
        </view>
      </view>
    </view>
    
    <!-- 吐槽内容卡片 -->
    <view class="content-card">
      <text class="content-title">
        <text class="required-mark">*</text>
        吐槽内容
      </text>
      <textarea
        class="content-textarea"
        placeholder="请填写"
        placeholder-style="color: #999;"
        v-model="form.rantContent"
        maxlength="500"
        :disable-scroll="true"
        @focus="handleContentFocus"
        @blur="handleContentBlur"
      ></textarea>
    </view>

    <!-- 措施卡片 -->
    <view class="content-card">
      <text class="content-title">措施</text>
      <textarea
        class="content-textarea disabled"
        placeholder-style="color: #999;"
        v-model="form.solution"
        maxlength="500"
        disabled
      ></textarea>
    </view>

    <!-- 驳回原因卡片 -->
    <view class="content-card" v-if="type === 'reject'">
      <text class="content-title">驳回原因</text>
      <textarea
        class="content-textarea disabled"
        placeholder="请填写"
        placeholder-style="color: #999;"
        v-model="form.approveDesc"
        maxlength="500"
        disabled
      ></textarea>
    </view>
    <!-- 滚动目标元素，用于精确定位 -->
    <view class="scroll-target" v-if="isContentFocused" style="height: 10vh;"></view>
    <!-- 底部按钮 -->
    <view class="bottom-buttons">
      <view class="btn btn-save" @tap="saveForm">保存</view>
      <view class="btn btn-submit" @tap="submitForm">提交</view>
    </view>

    <!-- 用户选择组件 -->
    <select-user ref="selectUser" @feedbackEmit="handleUserSelect" />
    
    <!-- 分类选择组件 -->
    <category-picker ref="categoryPicker" title="吐槽分类" dictType="rant_classify" :hasAll="false" @confirm="handleCategorySelect" />
    
    <!-- 部门选择组件 -->
    <department-picker ref="departmentPicker" title="部门选择" @confirm="handleDepartmentSelect" />
    
    <!-- 日期选择组件 -->
    <!-- <date-picker ref="datePicker" title="计划完成时间" @confirm="handleDateSelect" /> -->
  </view>
 
</template>

<script>
import { getDicts } from '@/api/common'
import { getInfo } from '@/api/login'
import { listRespdet, getRespdet } from '@/api/rant/respdet'
import { myRantAdd, myRantEdit, getMatters } from '@/api/rant/matters'
import SelectUser from '@/components/SelectUser/index.vue'
import CategoryPicker from '@/components/CategoryPicker/index.vue'
import DepartmentPicker from '@/components/DepartmentPicker/index.vue'
import DatePicker from '@/components/DatePicker/index.vue'

export default {
  components: {
    SelectUser,
    CategoryPicker,
    DepartmentPicker,
    DatePicker
  },
  data() {
    return {
      form: {
        id: null,
        ranter: null,
        ranterName: null,
        mattersType: '2',
        rantClassify: null,
        planTime: null,
        deptId: null,
        deptName: null,
        responsiblePerson: null,
        responsiblePersonName: null,
        respDeptResponsibler: null,
        respDeptResponsiblerName: null,
        rantContent: null,
        solution: null,
        status: 0
      },
      type: '', // update: 编辑， reject: 驳回后的编辑
      rantId: '',
      responsibleRoleId: '100', // 责任人角色ID
      isContentFocused: false, // 控制吐槽内容textarea焦点状态
    }
  },
  onLoad(options) {
    this.type = options.type
    this.rantId = options.id
    if (this.type === 'update' || this.type === 'reject') {
      this.handleUpdate(this.rantId)
    } else {
      this.initForm()
    }
  },
  methods: {
    handleChange(e) {
      this.form.planTime = e.detail.value
    },
    async initForm() {
      try {
        const res = await getInfo()
        this.form.ranter = res.user.userId
        this.form.ranterName = res.user.nickName
      } catch (error) {
        console.error('初始化表单失败:', error)
        uni.showToast({
          title: '加载数据失败',
          icon: 'none'
        })
      }
    },
    
    async handleUpdate(id) {
      try {
        const response = await getMatters(id)
        this.form = response.data
        this.responsiblePerson = response.data.responsiblePerson
        this.responsiblePersonName = response.data.responsiblePersonName
      } catch (error) {
        console.error('获取详情失败:', error)
        uni.showToast({
          title: '加载数据失败',
          icon: 'none'
        })
      }
    },
    
    // 显示选择器
    showTargetPicker() {
      this.$refs.departmentPicker.show()
    },
    
    showCategoryPicker() {
      this.$refs.categoryPicker.show()
    },
    
    showDatePicker() {
      this.$refs.datePicker.show()
    },
    
    showResponsiblePicker() {
      this.$refs.selectUser.roleId = this.responsibleRoleId
      this.$refs.selectUser.show()
    },
    
    showDepartmentPicker() {
      this.$refs.departmentPicker.show()
    },
    
    // 处理选择事件
    handleCategorySelect(category) {
      console.log(category)
      this.form.rantClassify = category
    },
    
    handleDepartmentSelect(dept) {
      console.log("handleDepartmentSelect---------", dept)
      this.form.deptId = dept.id
      this.form.deptName = dept.name
      this.form.respDeptResponsibler = dept.respPeople
      this.form.respDeptResponsiblerName = dept.respPeopleName
    },
    
    handleDateSelect(date) {
      this.form.planTime = date.date
    },
    
    // 表单验证
    validateForm(isSubmit = true) {
      /* if (!this.form.ranter) {
        uni.showToast({
          title: '吐槽人不能为空',
          icon: 'none'
        })
        return false
      } */
      /* if (!this.form.deptId) {
        uni.showToast({
          title: '吐槽对象不能为空',
          icon: 'none'
        })
        return false
      } */
      // 提交时才校验吐槽分类
      if (isSubmit && !this.form.rantClassify) {
        uni.showToast({
          title: '吐槽分类不能为空',
          icon: 'none'
        })
        return false
      }
     /*  if (!this.form.responsiblePerson) {
        uni.showToast({
          title: '责任人不能为空',
          icon: 'none'
        })
        return false
      } */
      // 提交时才校验吐槽内容
      if (isSubmit && (!this.form.rantContent || this.form.rantContent.length < 5)) {
        uni.showToast({
          title: '请填写吐槽内容，不少于5个字',
          icon: 'none'
        })
        return false
      }
      return true
    },
    
    // 保存表单
    async saveForm() {
      if (this.validateForm(true)) { // 保存时不校验吐槽分类和吐槽内容
        try {
          uni.showLoading({
            title: '保存中...',
            mask: true
          })
          
          this.form.status = 0 // 草稿状态
          if (this.form.id) {
            await myRantEdit(this.form)
          } else {
            await myRantAdd(this.form)
          }
          
          uni.hideLoading()
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })
          
          setTimeout(() => {
            uni.navigateBack()
            // uni.navigateTo({
            //   url: '/pages/myRant/index'
            // })
          }, 1500)
        } catch (error) {
          uni.hideLoading()
          console.error('保存失败:', error)
          uni.showToast({
            title: '保存失败',
            icon: 'none'
          })
        }
      }
    },
    
    // 提交表单
    async submitForm() {
      if (this.validateForm(true)) { // 提交时校验所有字段
        try {
          uni.showLoading({
            title: '提交中...',
            mask: true
          })
          
          this.form.status = 1 // 提交状态
          if (this.form.id) {
            await myRantEdit(this.form)
          } else {
            await myRantAdd(this.form)
          }
          
          uni.hideLoading()
          uni.showToast({
            title: '提交成功',
            icon: 'success',
            duration: 2000
          })
          
          setTimeout(() => {
           /*  uni.redirectTo({
              url: '/pages/myRant/index'
            }) */
            uni.navigateBack(-1)
          }, 2000)
        } catch (error) {
          uni.hideLoading()
          console.error('提交失败:', error)
          uni.showToast({
            title: '提交失败',
            icon: 'none'
          })
        }
      }
    },
    
    handleUserSelect(user) {
      this.form.responsiblePerson = user.userId
      this.form.responsiblePersonName = user.nickName
    },
    
    // 处理进度输入框获得焦点
    handleContentFocus() {
      // 使用智能焦点滚动
      this.$scrollHelper.smartFocusScroll(() => {
        // 重置并设置DOM状态
        this.isContentFocused = false;
        this.$nextTick(() => {
          this.isContentFocused = true;
        });
      }, {
        targetSelector: '.scroll-target',
        duration: 300,
        offset: -50,
        waitTime: 200, // 等待时间稍长一些，确保DOM完全更新
        success: () => {
          console.log('智能滚动成功');
        },
        fail: (error) => {
          console.warn('智能滚动失败:', error);
        }
      });
    },

    // 处理进度输入框失去焦点
    handleContentBlur() {
      // 延迟重置状态，避免键盘收起时的闪烁
      setTimeout(() => {
        this.isContentFocused = false;
        // 失去焦点时可选择滚动回顶部（注释掉以避免干扰用户操作）
        // this.$scrollHelper.scrollToTop({
        //   duration: 300
        // });
      }, 200);
    },
  }
}
</script>

<style lang="scss" scoped>
.create-rant-container {
  min-height: 100vh;
  background-color: #D6E1F1;
  padding: 24rpx 24rpx 132rpx;
}

.create-rant-container.keyboard-activ-create {
  padding-bottom: 30vh;
}

/* 步骤指示器样式 */
.step-indicator {
  padding: 24rpx;
  box-sizing: border-box;
  margin-bottom: 30rpx;
}

.step-container {
  position: relative;
  width: 100%;
}

.step-line-bg {
  position: absolute;
  height: 2rpx;
  background-color: #FFFFFF;
  top: 32rpx;
  left: 16%;
  right: 16%;
  z-index: 1;
}

.step-items {
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 33.33%;
}

.step-circle {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
}

.step-item.active .step-circle {
  /* background-color: #4080FF; */
  color: #FFFFFF;
}

.step-item.step-current .step-circle {
  /* background-color: #4080FF; */
  color: #FFFFFF;
}

.step-item.step-wait .step-circle {
  /* background-color: #F5F5F5; */
  color: #999999;
}

.step-item.step-finished .step-circle {
  /* background-color: #52C41A; */
  color: #FFFFFF;
}

.step-icon {
  font-size: 36rpx;
}

.step-icon-image {
  width: 64rpx;
  height: 64rpx;
}

.step-text {
  font-size: 26rpx;
  color: #333333;
}

/* 表单卡片样式 */
.form-card, .content-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 24rpx;
}

.form-header {
  display: flex;
  justify-content: space-between;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #EEEEEE;
  margin-bottom: 20rpx;
}

.form-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
}

.form-action {
  font-size: 28rpx;
  color: #666666;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 28rpx;
  color: #333333;
}

.form-value {
  display: flex;
  align-items: center;
}

.form-placeholder {
  font-size: 28rpx;
  color: #666666;
  margin-right: 10rpx;
}

.form-arrow {
  font-size: 28rpx;
  color: #999999;
}

/* 内容卡片样式 */
.content-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
}

.content-textarea {
  margin-top: 20rpx;
  width: 100%;
  height: 200rpx;
  font-size: 28rpx;
  color: #333333;
  padding: 10rpx 0;
  &.disabled {
    background-color: #F5F5F5;
  }
}
.disabled {
    background-color: #F5F5F5;
  }
/* 底部按钮样式 */
.bottom-buttons {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  padding: 20rpx 30rpx;
  background-color: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 30rpx;
  margin: 0 10rpx;
}

.btn-save {
  background-color: #E6EFFF;
  color: #4080FF;
}

.btn-submit {
  background-color: #4080FF;
  color: #FFFFFF;
}

/* 必填标记样式 */
.required-mark {
  color: #FF0000;
  margin-left: 4rpx;
}
</style>
