<template>
  <view :class="['feedback-container']">
    <!-- Feedback List -->
    <view v-for="(item, index) in feedbackList" :key="item.id" class="feedback-card">
      <!-- When multiple items exist, show the title+number, first one expanded by default -->
      <view v-if="feedbackList.length > 1" class="card-header" @tap="toggleCard(index)">
        <text class="card-title">督办事项 {{ index + 1 }}</text>
        <text class="card-toggle">{{ item.expanded ? '收起' : '展开' }}</text>
      </view>

      <!-- Card content - shown if single item or if expanded -->
      <view class="card-content" v-if="feedbackList.length === 1 || item.expanded">
        <!-- Basic Information -->
        <view class="section">
          <view class="section-header">
            <image class="header-icon" src="/static/images/icon-baseinfo.png" mode="aspectFit"></image>
            <text class="section-title">基本信息</text>
          </view>

          <view class="info-list">
            <!-- <view class="info-item">
                <text class="info-label">来源</text>
                <text class="info-value">{{ item.ranterName }}</text>
              </view> -->
            <view class="info-item">
              <text class="info-label">类型</text>
              <text class="info-value">{{ formatDictTag(item.mattersType, 'rant_matters_type') }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">分类</text>
              <text class="info-value">{{ formatDictTag(item.rantClassify, 'rant_classify') }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">内容</text>
              <text class="info-value">{{ item.rantContent || '--' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">措施</text>
              <text class="info-value">{{ item.solution || '--' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">责任人</text>
              <text class="info-value">{{ item.responsiblePersonName || '--' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">责任部门</text>
              <text class="info-value">{{ item.deptName || '--' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">责任部门负责人</text>
              <text class="info-value">{{ item.respDeptResponsiblerName || '--' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">分管领导</text>
              <text class="info-value">{{ item.respDeptLeaderName || '--' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">计划完成时间</text>
              <text class="info-value">{{ item.planTime || '--' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">是否保密</text>
              <text class="info-value">{{ item.isPrivate == 1 ? '是' : '否' }}</text>
            </view>
          </view>
        </view>

        <!-- Progress Submit Form -->
        <view class="section">
          <view class="section-header">
            <image class="header-icon" src="/static/images/icon-progress.png" mode="aspectFit"></image>
            <text class="section-title">本次进展<text class="required-mark">*</text></text>
          </view>

          <view class="progress-form">
            <textarea class="progress-input" placeholder="请输入内容" v-model="item.thisProgress"
              @focus="handleProgressFocus" @blur="handleProgressBlur"></textarea>

            <view class="form-options">
              <view class="form-item">
                <text class="form-label">是否结项<text class="required-mark">*</text></text>
                <view class="radio-group">
                  <view class="radio-button" @tap="setIsCompletion(index, true)">
                    <view :class="['radio-button-inner', { 'active': item.isCompletion }]">是</view>
                  </view>
                  <view class="radio-button" @tap="setIsCompletion(index, false)">
                    <view :class="['radio-button-inner', { 'active': !item.isCompletion }]">否</view>
                  </view>
                </view>
              </view>

              <view class="form-item" v-if="item.isCompletion">
                <text class="form-label">结项时间<text class="required-mark">*</text></text>
                <picker mode="date" :value="item.closingTime" :start="sevenDaysAgo"
                  @change="handleDateChange($event, index)" class="date-picker">
                  <view class="picker-value">
                    <uni-icons type="info" :size="20" style="margin-right: 10rpx;color: #376DF7;"></uni-icons>
                    {{ item.closingTime || '请选择时间' }}
                  </view>
                </picker>
              </view>

              <view class="form-item">
                <text class="form-label">成果<text class="required-mark" v-if="item.isCompletion">*</text></text>
                <view class="file-picker">
                  <view v-if="!item.fileList || item.fileList.length === 0" @tap.stop="chooseFile(index)">
                    <view class="file-picker-content">
                      <uni-icons type="folder-add" :size="20" style="margin-right: 10rpx;color: #376DF7;"></uni-icons>
                      <text class="custom-arrow-right">请上传文件</text>
                    </view>

                  </view>
                  <view v-else class="file-list">
                    <view v-for="(file, fileIndex) in item.fileList" :key="fileIndex" class="file-item">
                      <view class="file-name">
                        <view class="filename-part">{{ getTruncatedFileName(file.name) }}</view>
                        <view class="extension-part">{{ getFileExtension(file.name) }}</view>
                        <view class="file-delete" @tap.stop="deleteFile(index, fileIndex)">×</view>
                      </view>
                    </view>
                    <text class="add-more-files" @tap.stop="chooseFile(index)">添加文件</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- Progress History -->
        <view class="detail-card">
          <view class="card-header" style="justify-content: flex-start;">
            <image class="header-icon" src="/static/images/icon-progress.png" mode="aspectFit"></image>
            <text class="header-title">进度情况</text>
          </view>

          <view class="progress-list">
            <view class="progress-item" v-for="(fileItem, index) in item.recordDtoList" :key="index">
              <view class="progress-header">
                <view class="progress-index">{{ index + 1 }}</view>
                <view class="progress-date">{{ formatDate(fileItem.feedbackTime) }}</view>
              </view>
              <view class="progress-content">
                <rich-text class="progress-text" :nodes="fileItem.actualProgress"></rich-text>
                <view class="progress-files">
                  <view v-for="(file, fileIndex) in fileItem.fileList" :key="fileIndex" @tap="openFile(file.url)"
                    class="file-name">
                    <view class="filename-part">{{ getTruncatedFileName(file.name) }}</view>
                    <view class="extension-part">{{ getFileExtension(file.name) }}</view>
                  </view>
                </view>
              </view>
            </view>

            <view class="empty-state" v-if="!item.recordDtoList || item.recordDtoList.length === 0">
              <text>暂无进度记录</text>
            </view>
          </view>
        </view>
        <!-- 滚动目标元素，用于精确定位 -->
        <view class="scroll-target" v-if="isContentFocused" style="height: 10vh;"></view>
      </view>
    </view>

    <!-- Bottom Buttons -->
    <view class="bottom-buttons">
      <button class="btn-save" @tap="submitForm(0)" :disabled="submitting">
        <text v-if="savingLoading" class="loading-icon">&#xe603;</text>
        <text>{{ savingLoading ? '保存中...' : '保存' }}</text>
      </button>
      <button class="btn-submit" @tap="submitForm(1)" :disabled="submitting">
        <text v-if="submittingLoading" class="loading-icon">&#xe603;</text>
        <text>{{ submittingLoading ? '提交中...' : '提交' }}</text>
      </button>
    </view>
  </view>
</template>

<script>
import { feedbackRankMatter } from '@/api/rant/matters';
import { submitFeedback } from '@/api/rant/record';
import { getDicts, uploadFileMultiple } from '@/api/common';
import ConfigIndex from '@/config/index.config.js';

export default {
  data() {
    return {
      feedbackList: [],
      loading: true,
      savingLoading: false,
      submittingLoading: false,
      submitting: false,
      dictData: {
        rant_classify: [],
        rant_matters_type: []
      },
      id: null,
      isContentFocused: true,
      isChoosingFile: false,
    }
  },
  computed: {
    sevenDaysAgo() {
      const date = new Date();
      date.setDate(date.getDate() - 7); // 计算7天前的日期
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id;
      this.getDict();
      this.fetchFeedbackDetail(options.id);
    } else {
      uni.showToast({
        title: '参数错误',
        icon: 'none'
      });
    }
  },
  methods: {
    // 处理进度输入框获得焦点
    handleProgressFocus() {
      // 使用智能焦点滚动
      this.$scrollHelper.smartFocusScroll(() => {
        // 重置并设置DOM状态
        this.isContentFocused = false;
        this.$nextTick(() => {
          this.isContentFocused = true;
        });
      }, {
        targetSelector: '.scroll-target',
        duration: 300,
        offset: -50,
        waitTime: 200, // 等待时间稍长一些，确保DOM完全更新
        success: () => {
          console.log('智能滚动成功');
        },
        fail: (error) => {
          console.warn('智能滚动失败:', error);
        }
      });
    },

    // 处理进度输入框失去焦点
    handleProgressBlur() {
      // 延迟重置状态，避免键盘收起时的闪烁
      setTimeout(() => {
        this.isContentFocused = false;
        // 失去焦点时可选择滚动回顶部（注释掉以避免干扰用户操作）
        // this.$scrollHelper.scrollToTop({
        //   duration: 300
        // });
      }, 200);
    },

    // 打开文件
    openFile(url) {
      if (!url) return;
      console.log('openFile-----------', url);
      uni.showLoading({
        title: '打开中...'
      });

      // 检查文件类型
      const fileType = this.getFileType(url);

      // 如果是图片，直接使用预览图片
      if (fileType === 'image') {
        uni.previewImage({
          urls: [url],
          current: url,
          success: () => {
            uni.hideLoading();
          },
          fail: () => {
            uni.hideLoading();
            uni.showToast({
              icon: 'none',
              title: '预览图片失败'
            });
          }
        });
        return;
      }

      // 其他类型文件使用下载后打开
      uni.downloadFile({
        url: url,
        success: (res) => {
          console.log('res', res);
          uni.hideLoading();
          if (res.statusCode === 200) {
            uni.openDocument({
              filePath: res.tempFilePath,
              fileType: fileType,
              success: () => {
                console.log('文件打开成功');
              },
              fail: (error) => {
                console.error('打开文件失败:', error);
                uni.showToast({
                  icon: 'none',
                  title: '打开文件失败'
                });
              }
            });
          } else {
            uni.showToast({
              icon: 'none',
              title: '下载文件失败'
            });
          }
        },
        fail: (error) => {
          uni.hideLoading();
          console.error('下载文件失败:', error);
          uni.showToast({
            icon: 'none',
            title: '下载文件失败'
          });
        }
      });
    },

    // 获取文件类型
    getFileType(url) {
      const extension = url.split('.').pop().toLowerCase();
      const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
      const documentTypes = {
        'pdf': 'pdf',
        'doc': 'doc',
        'docx': 'docx',
        'xls': 'xls',
        'xlsx': 'xlsx',
        'ppt': 'ppt',
        'pptx': 'pptx',
        'txt': 'txt'
      };

      if (imageTypes.includes(extension)) {
        return 'image';
      }

      return documentTypes[extension] || 'unknown';
    },
    getTruncatedFileName(fileName) {
      const extension = this.getFileExtension(fileName);
      return fileName.replace(extension, '');
    },
    getFileExtension(fileName) {
      const lastDotIndex = fileName.lastIndexOf('.');
      if (lastDotIndex === -1) return '';
      return fileName.substring(lastDotIndex);
    },
    // 获取数据字典
    async getDict() {
      try {
        const dictTypes = ['rant_classify', 'rant_matters_type'];
        const promises = dictTypes.map(type => getDicts(type));
        const results = await Promise.all(promises);

        results.forEach((res, index) => {
          if (res.code === 200) {
            this.dictData[dictTypes[index]] = res.data;
          }
        });
      } catch (error) {
        console.error('获取数据字典失败', error);
      }
    },

    // 格式化字典标签
    formatDictTag(value, dictType) {
      if (!value || !this.dictData[dictType]) return '';

      if (dictType === 'rant_matters_type' && value.includes(',')) {
        const types = value.split(',');
        return types.map(type => {
          const dictItem = this.dictData[dictType].find(item => item.dictValue === type);
          return dictItem ? dictItem.dictLabel : type;
        }).join(', ');
      } else {
        const dictItem = this.dictData[dictType].find(item => item.dictValue === value);
        return dictItem ? dictItem.dictLabel : value;
      }
    },

    // 获取督办事项详情
    async fetchFeedbackDetail(id) {
      this.loading = true;
      try {
        const res = await feedbackRankMatter(id);
        if (res.code === 200) {
          this.feedbackList = [{
            ...res.data,
            expanded: true, // 默认展开
            // thisProgress: '', // 当前进展内容
            // isCompletion: false, // 是否结项
            // closingTime: '', // 结项时间
            // fileList: [] // 上传文件列表
          }];
        } else {
          uni.showToast({
            title: res.msg || '获取数据失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取督办事项详情失败', error);
        uni.showToast({
          title: '获取数据失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 设置是否结项
    setIsCompletion(index, value) {
      this.$set(this.feedbackList[index], 'isCompletion', value);
    },

    // 切换卡片展开/收起
    toggleCard(index) {
      this.$set(this.feedbackList[index], 'expanded', !this.feedbackList[index].expanded);
    },

    // 日期选择处理
    handleDateChange(e, index) {
      this.$set(this.feedbackList[index], 'closingTime', e.detail.value);
    },

    // 选择文件 - 使用优化的文件选择工具
    async chooseFile(index) {
      if (this.isChoosingFile) return; // 如果正在选择文件，直接返回
      this.isChoosingFile = true; // 设置标志位
      wx.getSystemInfo({
        success: (res) => {
          const environment = res.environment;
          if (environment === 'wxwork') {
            // 企业微信环境
            wx.qy.chooseMessageFile({
              type: 'all',
              success: (res) => {
                this.uploadFiles(res.tempFiles, index);
                this.isChoosingFile = false; // 选择成功后重置标志位
              },
              fail: (err) => {
                console.error('选择文件失败', err);
                // 如果chooseMessageFile失败，尝试用chooseImage作为备选
                uni.chooseImage({
                  success: (res) => {
                    this.uploadFiles(res.tempFiles, index);
                    this.isChoosingFile = false; // 选择成功后重置标志位
                  },
                  fail: (imgErr) => {
                    console.error('选择图片也失败', imgErr);
                    this.isChoosingFile = false; // 失败后也要重置标志位
                  }
                });
              }
            });
          } else {
            // 普通微信环境
            // 微信小程序不支持chooseFile，使用chooseMessageFile代替
            uni.chooseMessageFile({
              type: 'all',
              success: (res) => {
                this.uploadFiles(res.tempFiles, index);
                this.isChoosingFile = false; // 选择成功后重置标志位
              },
              fail: (err) => {
                console.error('选择文件失败', err);
                // 如果chooseMessageFile失败，尝试用chooseImage作为备选
                uni.chooseImage({
                  success: (res) => {
                    this.uploadFiles(res.tempFiles, index);
                    this.isChoosingFile = false; // 选择成功后重置标志位
                  },
                  fail: (imgErr) => {
                    console.error('选择图片也失败', imgErr);
                    this.isChoosingFile = false; // 失败后也要重置标志位
                  }
                });
              }
            });
          }
        }
      });
    },

    // 上传文件
    async uploadFiles(files, index) {
      if (!files || files.length === 0) return;

      this.loading = true;
      try {
        // 微信小程序不支持FormData，需要使用uni.uploadFile
        const uploadPromises = files.map(file => {
          return new Promise((resolve, reject) => {
            uni.uploadFile({
              url: ConfigIndex.baseUrl + '/file/rantUploads',
              filePath: file.path || file.tempFilePath,
              name: 'files',
              header: {
                'Authorization': 'Bearer ' + uni.getStorageSync('access_token')
              },
              success: (uploadRes) => {
                // uploadRes.data是字符串，需要解析成JSON
                try {
                  const result = JSON.parse(uploadRes.data);
                  resolve(result);
                } catch (e) {
                  console.error('Parse error:', e, uploadRes.data);
                  reject(new Error('上传响应解析失败'));
                }
              },
              fail: (err) => {
                console.error('Upload failed:', err);
                reject(err);
              }
            });
          });
        });

        const results = await Promise.all(uploadPromises);
        const successfulUploads = results.filter(res => res.code === 200).flatMap(res => res.data || []);

        if (successfulUploads.length > 0) {
          // 如果之前没有文件列表，创建一个新的
          if (!this.feedbackList[index].fileList) {
            this.$set(this.feedbackList[index], 'fileList', []);
          }

          // 将上传的文件添加到列表
          this.feedbackList[index].fileList = [
            ...this.feedbackList[index].fileList,
            ...successfulUploads
          ];

          uni.showToast({
            title: '文件上传成功',
            icon: 'success'
          });
        } else {
          uni.showToast({
            title: '文件上传失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('文件上传失败', error);
        uni.showToast({
          title: '文件上传失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 删除文件
    deleteFile(index, fileIndex) {
      this.feedbackList[index].fileList.splice(fileIndex, 1);
    },
    // 获取文件类型
    getFileType(url) {
      const extension = url.split('.').pop().toLowerCase();
      const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
      const documentTypes = {
        'pdf': 'pdf',
        'doc': 'doc',
        'docx': 'docx',
        'xls': 'xls',
        'xlsx': 'xlsx',
        'ppt': 'ppt',
        'pptx': 'pptx',
        'txt': 'txt'
      };

      if (imageTypes.includes(extension)) {
        return 'image';
      }

      return documentTypes[extension] || 'unknown';
    },
    // 预览文件
    previewFile(url) {
      console.log('previewFile-----------', url);
      if (!url) return;

      uni.showLoading({
        title: '打开中...'
      });

      // 检查文件类型
      const fileType = this.getFileType(url);

      // 如果是图片，直接使用预览图片
      if (fileType === 'image') {
        uni.previewImage({
          urls: [url],
          current: url,
          success: () => {
            uni.hideLoading();
          },
          fail: () => {
            uni.hideLoading();
            uni.showToast({
              icon: 'none',
              title: '预览图片失败'
            });
          }
        });
        return;
      }

      // 其他类型文件使用下载后打开
      uni.downloadFile({
        url: url,
        success: (res) => {
          console.log('res', res);
          uni.hideLoading();
          if (res.statusCode === 200) {
            uni.openDocument({
              filePath: res.tempFilePath,
              fileType: fileType,
              success: () => {
                console.log('文件打开成功');
              },
              fail: (error) => {
                console.error('打开文件失败:', error);
                uni.showToast({
                  icon: 'none',
                  title: '打开文件失败'
                });
              }
            });
          } else {
            uni.showToast({
              icon: 'none',
              title: '下载文件失败'
            });
          }
        },
        fail: (error) => {
          uni.hideLoading();
          console.error('下载文件失败:', error);
          uni.showToast({
            icon: 'none',
            title: '下载文件失败'
          });
        }
      });
    },

    // 格式化日期
    formatDate(timestamp) {
      if (!timestamp) return '';

      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      return `${year}-${month}-${day}`;
    },

    // 提交反馈
    async submitForm(submitStatus) {
      if (this.feedbackList.length === 0) {
        uni.showToast({
          title: '无数据提交',
          icon: 'none'
        });
        return;
      }

      const item = this.feedbackList[0];

      if (!item.thisProgress) {
        uni.showToast({
          title: '请输入本次进展',
          icon: 'none'
        });
        return;
      }

      if (item.isCompletion) {
        if (!item.closingTime) {
          uni.showToast({
            title: '请选择结项时间',
            icon: 'none'
          });
          return;
        }

        if (!item.fileList || item.fileList.length === 0) {
          uni.showToast({
            title: '请上传成果文件',
            icon: 'none'
          });
          return;
        }
      }

      // 设置加载状态
      this.submitting = true;
      if (submitStatus === 0) {
        this.savingLoading = true;
      } else {
        this.submittingLoading = true;
      }
      uni.showLoading({
        title: '提交中...'
      });
      try {
        const data = {
          /*  rantMattersId: item.id,
           thisProgress: item.thisProgress,
           isCompletion: item.isCompletion,
           closingTime: item.closingTime,
           fileList: item.fileList, */
          ...item,
          rantMattersId: item.id,
          submitStatus: submitStatus
        };

        const res = await submitFeedback(data);
        uni.hideLoading();
        if (res.code === 200) {
          uni.showToast({
            title: submitStatus === 1 ? '提交成功' : '保存成功',
            icon: 'success',
            duration: 1500
          });
          /* uni.navigateTo({
            url: '/pages/myTask/index'
          }) */
          setTimeout(() => {
            uni.navigateBack(-1)
          }, 1500)
          // uni.navigateBack(-1)
        } else {
          uni.showToast({
            title: res.msg || '操作失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('提交进度失败', error);
        uni.showToast({
          title: '提交失败',
          icon: 'none'
        });
      } finally {
        this.submitting = false;
        this.savingLoading = false;
        this.submittingLoading = false;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.keyboard-active-matters {
  margin-bottom: 30vh;
}

.file-name {
  width: 100%;
  display: flex;
  margin-top: 10rpx;

  &:first-child {
    margin-top: 0;
  }

  .filename-part {
    min-width: 0;
    flex-grow: 1;
    word-break: keep-all;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .extension-part {
    word-break: keep-all;
  }
}

.feedback-container {
  padding: 24rpx 24rpx 132rpx;
  background-color: #D6E1F1;
  // height: 100vh;
  // overflow-y: auto;
}

.feedback-card {
  background: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 24rpx;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
}

.card-title {
  font-weight: 500;
  font-size: 28rpx;
  color: #333333;
}

.card-toggle {
  font-size: 24rpx;
  color: #666666;
}

.section {
  margin-bottom: 30rpx;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-icon {
  width: 36rpx;
  height: 36rpx;
  background: #FF9500;
  border-radius: 50%;
  text-align: center;
  line-height: 36rpx;
  color: #FFFFFF;
  font-size: 24rpx;
  margin-right: 10rpx;
}

.section-title {
  font-weight: 500;
  font-size: 28rpx;
  color: #333333;
}

.header-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 20rpx;
}

.info-list {
  display: flex;
  flex-direction: column;
}

.info-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
}

.info-item-multi {
  flex-direction: column;
}

.info-label {
  font-size: 26rpx;
  color: #333333;
  min-width: 200rpx;
}

.info-value {
  font-size: 26rpx;
  color: #333333;
  flex: 1;
  font-weight: 600;
}

.info-item-multi .info-value {
  margin-top: 10rpx;
}

.progress-form {
  padding: 20rpx 0;
}

.progress-input {
  width: 100%;
  height: 180rpx;
  background: #F5F5F5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 26rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.form-options {
  display: flex;
  flex-direction: column;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
}

.form-label {
  font-size: 26rpx;
  color: #333333;
  min-width: 150rpx;
}

.radio-group {
  display: flex;
  justify-content: flex-end;
  flex: 1;
}

.radio-button {
  // flex: 1;
  width: 120rpx;
  height: 56rpx;
  // margin-right: 10px;
}

.radio-button-inner {
  width: 100%;
  height: 100%;
  // border-radius: 4px;
  border: 1px solid #DDDDDD;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #333333;
  background-color: #FFFFFF;
}

.radio-button-inner.active {
  background-color: #4080FF;
  color: #FFFFFF;
  border-color: #4080FF;
}

.date-picker,
.file-picker {
  flex: 1;
  font-size: 26rpx;
  color: #666666;
  text-align: right;
  min-width: 0;
}

.file-picker-content {
  display: flex;
  align-items: center;
  justify-content: flex-end
}

.picker-value {
  padding: 10rpx 0;
  display: flex;
  align-items: center;
  justify-content: flex-end
}

.file-list {
  display: flex;
  flex-direction: column;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.file-name {
  max-width: 480rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #376DF7;
}

.file-delete {
  color: #FF5151;
  font-size: 32rpx;
  font-weight: bold;
  padding: 0 10rpx;
}

.add-more-files {
  color: #4080FF;
  font-size: 26rpx;
  margin-top: 10rpx;
}

.progress-timeline {
  position: relative;
  padding: 20rpx 0;
}

.timeline-item {
  position: relative;
  padding-left: 30rpx;
  margin-bottom: 30rpx;
}

.timeline-point {
  position: absolute;
  left: 0;
  top: 10rpx;
  width: 16rpx;
  height: 16rpx;
  background: #4080FF;
  border-radius: 50%;
}

.timeline-line {
  position: absolute;
  left: 8rpx;
  top: 26rpx;
  width: 2rpx;
  height: calc(100% + 14rpx);
  background: #4080FF;
}

.timeline-content {
  padding-left: 20rpx;
}

.timeline-date {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 6rpx;
}

.timeline-title {
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 10rpx;
}

.file-link {
  font-size: 24rpx;
  color: #4080FF;
  margin-bottom: 5rpx;
}

.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 24rpx;
  background: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.btn-save,
.btn-submit {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-save {
  background: #F5F5F5;
  color: #333333;
  margin-right: 24rpx;
}

.btn-save[disabled],
.btn-submit[disabled] {
  opacity: 0.6;
}

.btn-submit {
  background: #4080FF;
  color: #FFFFFF;
}

.loading-icon {
  display: inline-block;
  animation: loading-rotate 1s linear infinite;
  margin-right: 10rpx;
  font-family: "iconfont";
}

@keyframes loading-rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #F0F0F0;
}

progress-list {
  padding: 5px 0;
}

.progress-item {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #F0F0F0;
}

.progress-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.progress-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.progress-index {
  width: 20px;
  height: 20px;
  background-color: #4080FF;
  color: #FFFFFF;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  margin-right: 10px;
}

.progress-date {
  font-size: 12px;
  color: #999999;
}

.progress-content {
  padding-left: 30px;
}

.progress-text {
  font-size: 14px;
  color: #333333;
  line-height: 1.6;
  margin-bottom: 8px;
}

.progress-files {
  margin-top: 5px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 20px 0;
  color: #999999;
  font-size: 14px;
}
</style>

<!-- 引入iconfont字体 -->
<style>
@font-face {
  font-family: 'iconfont';
  src: url('data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAAKAAAsAAAAABkQAAAI1AAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCCcApcbgE2AiQDCAsGAAQgBYRtBzAblQXIrjBu4UkbIuXbRKpkqR+wqgSn4OGfse+Xu/NdoklqTKIn0USCRBohQiGSIJEOofOQ8P/f73XOf+7CpcMgU01XVT1VD1rRIFshS9ijyD70W9hL0/XJemDnEwXkwjgw3gKFuM+vL2DfuUscR5MAlxPoBppiN6qLKsDCwFMC8Yoz2wFWKKzSwpyQGrRuyGL8QKV52k4A79H344dYFklNZrY9Oj9woPCDn9+mReMNAvEQgdMZI2MTKMS11pmTQsA4pNE3u9RIAa8qwQ++/lZpfVEJqrtL7AHRqyL4iaJJYE+yeZeP5Ksr66vA21WmfvL8/qNH/cePBx4+7H/4sLssSb+c7szs9fTlCG1jlMVsXtYupXYxy9+e1wRw55r3ru3UfDPfZO16vTfH/a9YA/gfvckHKATtrvM/tAOo7TxAO+YxbTAu2BgGBhYGFkqGcTNUVlZtGzZrUcMrOpuXkDfp0J2QOhlZc3NasHZU6jShWmsTmq1bPt7SQ0FCFPQyGyCMOkEy7BHZqFu0YB9RmfaK6mgENN8L11ZbFsEK7cAYoRd1mBVTJBqxuRaMFdI5JWqOczJnLsmohXAUOS5hcIpZsSNR0hl742g5R2tcoXDOklhMERmCNTKnDGMprFLOH+Mw61GUsxZHBbRTgzCMQE+kgyljFCKNsHE1MH1/SjScJlHGOW6U4SyR0I6Cw6S4j4VJrhnTEiq5xPTG0eLRGq6goBxLRJNhFCKGwDSSo5ggLIWnKMdvjrOwdcncf71L3DfgWnGqSJGjRFmp3AeVhjmZUQraSgAAAAA=') format('woff2');
  font-weight: normal;
  font-style: normal;
}
</style>
