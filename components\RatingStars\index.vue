<template>
  <view class="rating-stars" @touchstart="onTouchStart" @touchmove="onTouchMove" @touchend="onTouchEnd">
    <view v-for="(item, idx) in max" :key="idx" class="star-item" :id="`star-${idx}`" @tap="onStarTap(idx, $event)">
      <uni-icons v-if="getStarType(idx) === 'full'" type="star-filled" :color="activeColor" :size="size" />
      <uni-icons v-else-if="getStarType(idx) === 'half'" type="starhalf" :color="activeColor" :size="size" />
      <uni-icons v-else type="star" color="#ccc" :size="size" />
    </view>
  </view>
</template>

<script>
export default {
  name: 'RatingStars',
  props: {
    value: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: 5
    },
    readonly: {
      type: Boolean,
      default: false
    },
    allowHalf: {
      type: Boolean,
      default: true
    },
    activeColor: {
      type: String,
      default: '#FFD700' // 亮黄
    },
    size: {
      type: [Number, String],
      default: 32
    }
  },
  data() {
    return {
      hoverValue: null,
      touching: false,
      touchStartTime: 0,
      touchStartX: 0,
      hasMoved: false,
      starRects: [] // 缓存星星位置信息
    }
  },
  watch: {
    value() {
      // 外部强制改变评分时，立即重置悬浮值
      this.hoverValue = null
    }
  },
  computed: {
    /** 组件当前应展示的分值：悬浮优先生效 */
    displayValue() {
      return this.hoverValue !== null ? this.hoverValue : this.value
    }
  },
  methods: {
    /** 根据下标判断星星应呈现 empty / half / full */
    getStarType(idx) {
      const val = this.displayValue
      if (val >= idx + 1) return 'full'
      if (this.allowHalf && val >= idx + 0.5) return 'half'
      return 'empty'
    },

    /** 获取并缓存星星位置信息 */
    async getStarRects() {
      return new Promise((resolve) => {
        uni.createSelectorQuery()
          .in(this)
          .selectAll('.star-item')
          .boundingClientRect(list => {
            this.starRects = list || []
            console.log('🔥 缓存星星位置:', this.starRects.length)
            resolve(this.starRects)
          })
          .exec()
      })
    },

    /** 点击星星 */
    onStarTap(idx, e) {
      if (this.readonly) return

      // 如果刚刚进行了滑动，不执行点击
      if (this.hasMoved) {
        console.log('🔥 检测到滑动，忽略点击事件')
        return
      }

      const halfValue = idx + 0.5
      const fullValue = idx + 1
      let newValue = 0

      console.log('🔥=== 点击星星调试 ===')
      console.log('🔥 点击星星索引:', idx)
      console.log('🔥 当前 this.value:', this.value)

      // 判断当前点击星星的状态
      if (this.allowHalf) {
        const state =
          this.value >= fullValue ? 'full'
            : this.value >= halfValue ? 'half'
              : 'empty'

        console.log('🔥 当前星星状态:', state)

        // 正确的状态循环：empty → half → full → empty
        if (state === 'empty') {
          newValue = halfValue                    // 未选中 → 半星
          console.log('🔥 未选中 → 半星')
        } else if (state === 'half') {
          newValue = fullValue                    // 半星 → 整星
          console.log('🔥 半星 → 整星')
        } else { // full
          newValue = idx > 0 ? idx : 0           // 整星 → 未选中（回到前一个整星状态）
          console.log('🔥 整星 → 未选中')
        }
      } else {
        newValue = this.value === fullValue ? 0 : fullValue  // 整星 ↔ 未选中
      }

      console.log('🔥 计算出的新分数:', newValue)

      // 清除悬浮值，立即发送事件
      this.hoverValue = null

      console.log('🔥 点击发送分数:', newValue)
      this.$emit('input', newValue)
      this.$emit('change', newValue)
      this.$emit('set', newValue)

      console.log('🔥=== 点击星星调试结束 ===')
    },

    /** 触摸开始 */
    async onTouchStart(e) {
      if (this.readonly) return

      this.touching = true
      this.hasMoved = false
      this.touchStartTime = Date.now()
      this.touchStartX = e.touches[0].clientX

      console.log('🔥 触摸开始，位置:', this.touchStartX)
    },

    /** 触摸移动 */
    onTouchMove(e) {
      if (!this.touching || this.readonly) return

      const currentX = e.touches[0].clientX
      const moveDistance = Math.abs(currentX - this.touchStartX)

      // 降低滑动触发阈值，提高灵敏度
      if (moveDistance > 5) {
        this.hasMoved = true
        // 直接使用实时查询，确保准确性
        this.updateHoverValueRealtime(e)
      }
    },

    /** 触摸结束 */
    onTouchEnd(e) {
      if (!this.touching || this.readonly) return

      console.log('🔥=== 触摸结束调试 ===')
      console.log('🔥 hasMoved:', this.hasMoved)
      console.log('🔥 hoverValue:', this.hoverValue)
      console.log('🔥 当前 this.value:', this.value)

      this.touching = false

      // 如果是滑动操作且有悬浮值，确定分数（允许滑动到0分）
      if (this.hasMoved && this.hoverValue !== null) {
        const finalValue = this.hoverValue

        console.log('🔥 滑动发送分数:', finalValue)

        this.$emit('input', finalValue)
        this.$emit('change', finalValue)
        this.$emit('set', finalValue)
      } else {
        console.log('🔥 滑动未发送 - hasMoved:', this.hasMoved, 'hoverValue:', this.hoverValue)
      }

      // 清除悬浮值
      this.hoverValue = null

      // 延迟重置hasMoved
      setTimeout(() => {
        this.hasMoved = false
      }, 100)

      console.log('🔥=== 触摸结束调试结束 ===')
    },

    /** 实时查询位置并更新悬浮值 */
    updateHoverValueRealtime(e) {
      const touches = e.touches || (e.detail && e.detail.touches)
      if (!touches || !touches[0]) return
      const x = touches[0].clientX

      console.log('🔥 实时查询位置:', x)

      uni.createSelectorQuery()
        .in(this)
        .selectAll('.star-item')
        .boundingClientRect(list => {
          if (!list || list.length === 0) {
            console.log('🔥 警告：查询到的星星位置为空')
            return
          }

          console.log('🔥 查询到星星位置:', list.map((r, i) => `星星${i}: ${r.left}-${r.right}`))

          let value = 0
          let found = false

          // 检查是否在第一颗星之前（左边）
          if (x < list[0].left) {
            value = 0
            found = true
            console.log(`🔥 在第一颗星左边，设置为0分`)
          } else {
            // 正常的星星检测逻辑
            for (let i = 0; i < list.length; i++) {
              const rect = list[i]
              console.log(`🔥 检查星星${i}: 位置${x} 在 ${rect.left}-${rect.right} 范围内?`)

              if (x >= rect.left && x <= rect.right) {
                const isLeftHalf = this.allowHalf && x < rect.left + rect.width / 2
                value = isLeftHalf ? i + 0.5 : i + 1
                found = true
                console.log(`🔥 ✅ 命中星星${i}, 左半部=${isLeftHalf}, 计算分数=${value}`)
                break
              } else if (x > rect.right) {
                // 如果触摸位置超过当前星星右边界，说明至少选中了这颗星
                value = i + 1
                console.log(`🔥 超过星星${i}右边界, 当前累积分数=${value}`)
              }
            }

            if (!found) {
              // 如果没有命中任何星星，可能在最后一颗星右边
              if (x > list[list.length - 1].right) {
                value = this.max // 设置为最大值
                console.log(`🔥 在最后一颗星右边，设置为最大值: ${value}`)
              }
            }
          }

          console.log(`🔥 最终计算分数: ${value}`)

          // 限制范围，但允许0分
          value = Math.max(0, Math.min(value, this.max))

          // 强制更新悬浮值，确保滑动时的实时反馈
          if (this.hoverValue !== value) {
            console.log(`🔥 更新悬浮值: ${this.hoverValue} -> ${value}`)
            this.hoverValue = value
            this.$emit('set', value)
            this.$emit('input', value)
            this.$emit('change', value)
          } else {
            console.log(`🔥 悬浮值未变化: ${value}`)
          }
          
          // 确保视图能够实时更新
          this.$forceUpdate && this.$forceUpdate()
        })
        .exec()
    },

    /** 根据触摸位置计算半/整星悬浮值（备用方法） */
    updateHoverValue(e) {
      const touches = e.touches || (e.detail && e.detail.touches)
      if (!touches || !touches[0]) return
      const x = touches[0].clientX

      uni.createSelectorQuery()
        .in(this)
        .selectAll('.star-item')
        .boundingClientRect(list => {
          let value = 0
          for (let i = 0; i < list.length; i++) {
            const rect = list[i]
            if (x >= rect.left && x <= rect.right) {
              value = this.allowHalf && x < rect.left + rect.width / 2
                ? i + 0.5
                : i + 1
              break
            } else if (x > rect.right) {
              value = i + 1
            }
          }
          this.hoverValue = value
        })
        .exec()
    }
  }
}
</script>

<style scoped>
.rating-stars {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.star-item {
  margin-right: 8rpx;
  display: flex;
  align-items: center;
  min-width: 44rpx;
  min-height: 44rpx;
}

.star-item:last-child {
  margin-right: 0;
}
</style>
