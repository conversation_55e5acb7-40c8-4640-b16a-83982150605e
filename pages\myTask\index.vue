<template>
  <view class="task-container">

    <!-- Filter Area -->
    <view class="filter-area">
      <view class="filter-item" @tap="openCategoryPopup">
        <text>{{ selectedCategory === '全部' ? '类型' : selectedCategory }}</text>
        <text class="arrow-down">▼</text>
      </view>
      <view class="filter-item" @tap="openStatusPopup">
        <text>{{ getStatusLabel(selectedStatus) || '状态' }}</text>
        <text class="arrow-down">▼</text>
      </view>
      <view class="search-box">
        <!-- <text class="icon-search">🔍</text> -->
        <uni-icons class="icon-search" type="search" size="20"></uni-icons>
        <input type="text" placeholder="搜索内容关键词" v-model="searchText" confirm-type="search" @confirm="onSearch"
          class="search-input" @input="handleSearchInput" />
      </view>
    </view>

    <!-- Task List -->
    <scroll-view scroll-y class="task-scroll-view" refresher-enabled :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh" @scrolltolower="loadMoreData">
      <view class="task-list">
        <view v-for="task in taskList" :key="task.id" class="task-card" @tap="navigateToDetail(task.id)">
          <view class="task-header">
            <!-- 督办类型 -->
            <text class="task-title">{{ getTaskTypeLabel(task.mattersType) || task.title }}</text>
            <!-- 事项状态 -->
            <view :class="['task-status', getStatusClass(task.status)]">{{ getStatusText(task.status) }}</view>
          </view>
          <view class="task-subheader">
            <view class="person-type">
              <!-- 来源 -->
              <!-- <text class="task-person">{{task.ranterName}}</text> -->
              <!-- 责任人 -->
              <text class="task-person">{{ task.responsiblePersonName || '--' }}</text>

              <!-- <text class="task-person">{{task.responsiblePersonName || task.person}}</text> -->
              <!-- <text class="task-type">{{task.rantClassify || task.type}}</text> -->
            </view>
            <!-- 分类 -->
            <text class="task-type">{{ task.rantClassify }}</text>
          </view>
          <!-- 督办内容 -->
          <view class="task-content">
            <text>{{ task.rantContent }}</text>
          </view>
          <!-- 如果督办事项状态为按期完成或者延期完成，就显示第四行 左边是结项时间标题，右边是结项日期 -->
          <view v-if="task.status == 2 || task.status == 3" class="task-content cu-flex-between">
            <text>结项时间</text>
            <text>{{ task.closingTime || "--" }}</text>
          </view>
          <view class="task-actions">
            <view v-if="task.status == 1 || task.status == 4" class="action-btn feedback-btn"
              @tap.stop="handleProgressFeedback(task)">进度反馈</view>
          </view>
        </view>

        <!-- Loading & Empty States -->
        <view class="loading-state" v-if="loading">
          <text>加载中...</text>
        </view>
        <view class="empty-state" v-if="!loading && taskList.length === 0">
          <text>暂无任务</text>
        </view>
        <view class="no-more" v-if="!hasMore && taskList.length > 0">
          <text>没有更多数据了</text>
        </view>
      </view>
    </scroll-view>

    <!-- Category Popup -->
    <CategoryPicker ref="categoryPopupMattersType" :dictType="'rant_matters_type'" :multiple="true"
      @confirm="selectMattersType" />

    <!-- Status Popup -->
    <StatusPicker ref="statusPicker" @confirm="selectStatus" :multiple="true"/>
  </view>
</template>

<script>
import { getMatters, myTaskList } from '@/api/rant/matters';
import { submitFeedback } from '@/api/rant/record';
import { getDicts } from '@/api/common';
import { getInfo } from '@/api/login';
import CategoryPicker from '@/components/CategoryPicker';
import StatusPicker from '@/components/StatusPicker';
import { rantStatusOption } from "@/constant/index";
import mixin from '@/mixins/mixin';
export default {
  mixins: [mixin],
  components: {
    CategoryPicker,
    StatusPicker
  },
  data() {
    return {
      taskList: [],
      categories: [
        { id: 0, name: '全部' }
      ],
      statuses: [
        { value: '', label: '全部' },
        { value: 0, label: '草稿' },
        { value: 1, label: '进行中' },
        { value: 2, label: '按时完成' },
        { value: 3, label: '延期完成' },
        { value: 4, label: '延期未完成' },
        { value: 5, label: '终止' },
        { value: 6, label: '审批中' },
        { value: 7, label: '驳回' }
      ],
      selectedCategory: '全部',
      selectedStatus: '全部',
      selectedStatusValue: null,
      sortOrder: '',
      loading: false,
      searchText: '',
      // 分页参数
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        hasMore: true
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mattersType: [],
        rantClassify: null,
        responsiblePerson: null,
        status: null,
        planTime: null,
        closingTime: null,
        rantContent: null
      },
      hasMore: true,
      isRefreshing: false, // 控制下拉刷新状态
    }
  },
  onLoad() {
    console.log('onLoad triggered');
  },
  onShow() {
    console.log('onShow triggered');
    this.resetPagination();
    this.fetchTaskList();
  },

  methods: {
    /*getStatusClass(status) {
      const statusValue = Number(status);
      const statusObj = rantStatusOption.find(s => s.value === statusValue);
      
      if (!statusObj) return '';
      
      switch(statusValue) {
        case 0: // 草稿
          return 'status-draft';
        case 1: // 进行中
          return 'status-in-progress';
        case 2: // 按时完成
          return 'status-on-time';
        case 3: // 延期完成
          return 'status-delayed-finished';
        case 4: // 延期未完成
          return 'status-delayed-unfinished';
        case 5: // 终止
          return 'status-ended';
        case 6: // 审批中
          return 'status-approving';
        case 7: // 驳回
          return 'status-rejected';
        default:
          return '';
      }
    },*/
    // 重置分页参数
    resetPagination() {
      this.pagination = {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        hasMore: true
      };
      this.taskList = [];
    },

    /** 下拉刷新 */
    onRefresh() {
      this.isRefreshing = true;
      this.resetPagination();
      this.fetchTaskList();
    },

    /** 上拉加载更多 */
    loadMoreData() {
      if (this.pagination.hasMore && !this.loading) {
        this.pagination.pageNum++;
        this.fetchTaskList(true);
      }
    },
    getStatusLabel(value) {
      if (value === undefined || value === null || value === '') return '状态';
      const status = this.statuses?.find(s => s.value == value);
      return status ? status.label : '状态';
    },
    handleSearchInput() {
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout);
      }

      this.searchTimeout = setTimeout(() => {
        this.fetchTaskList();
      }, 500); // 500ms防抖
    },
    navigateToDetail(taskId) {
      uni.navigateTo({
        url: `/pages/look/detail?id=${taskId}&type=myTask&showRanterName=true`
      });
    },
    // 获取用户信息
    async getUserInfo() {
      try {
        const res = await getInfo();
        if (res.code === 200) {
          this.queryParams.responsiblePerson = res.user.userId;
          this.fetchTaskList();
        }
      } catch (error) {
        console.error('Get user info error:', error);
        this.fetchTaskList();
      }
    },

    handleView(task) {
      uni.navigateTo({
        url: `/pages/look/detail?id=${task.id}&type=myTask`
      });
    },

    handleProgressFeedback(task) {
      uni.navigateTo({
        url: `/pages/feedback/index?id=${task.id}`
      });
    },

    // Fetch data from API
    async fetchTaskList(isLoadMore = false) {
      if (this.loading) return;
      this.loading = true;

      try {
        // 创建查询参数，过滤掉空值
        const params = {
          ...this.queryParams,
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize
        };

        // 只添加非空的查询参数
        Object.keys(params).forEach(key => {
          if (params[key] === null ||
            params[key] === undefined ||
            params[key] === '' ||
            (Array.isArray(params[key]) && params[key].length === 0)) {
            delete params[key];
          }
        });

        // 添加搜索内容
        if (this.searchText) {
          params.rantContent = this.searchText;
        }

        console.log('Calling myTaskList with params:', params);

        try {
          const res = await myTaskList(params);
          console.log('myTaskList response:', res);

          if (res.code === 200) {
            let newList = res.rows || res.data || [];
            const total = res.total || 0;

            // 更新分页信息
            this.pagination.total = total;
            this.pagination.hasMore = this.taskList.length + newList.length < total;

            // 应用排序（如果需要）
            if (this.sortOrder) {
              newList = this.sortTasks(newList, this.sortOrder);
            }

            // 如果是加载更多，则追加数据
            if (isLoadMore) {
              this.taskList = [...this.taskList, ...newList];
            } else {
              this.taskList = newList;
            }
            this.hasMore = this.taskList.length < total;
            console.log('taskList updated:', this.taskList);
          } else {
            throw new Error('API returned error code');
          }
        } catch (apiError) {
          console.error('API call failed:', apiError);
          if (!isLoadMore) {
            this.taskList = [];
          }
        }
      } catch (error) {
        console.error('Fetch task list error:', error);
        if (!isLoadMore) {
          this.taskList = [];
        }
        uni.showToast({
          title: '获取任务列表失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        this.isRefreshing = false;
      }
    },

    async fetchCategories() {
      try {
        // 使用getDicts API获取字典数据
        const res = await getDicts('rant_matters_type');
        if (res.code === 200 && res.data) {
          this.categories = res.data;
        }
      } catch (error) {
        console.error('Fetch categories error:', error);
      }
    },

    // Dropdown actions
    openCategoryPopup() {
      this.$refs.categoryPopupMattersType.show();
    },

    // 处理类型选择
    selectMattersType(value) {
      if (value) {
        const category = this.categories?.find(c => c.dictValue == value);
        this.selectedCategory = category ? category.dictLabel : '全部';
        this.queryParams.mattersType = [value];
      } else {
        this.selectedCategory = '全部';
        this.queryParams.mattersType = [];
      }
      this.fetchTaskList();
    },

    openStatusPopup() {
      this.$refs.statusPicker.show();
    },

    selectStatus(statusData) {
      console.log("selectStatus---------", statusData)
      this.selectedStatus = statusData.value;
      this.queryParams.status = statusData.value;
      this.fetchTaskList();
    },

    // 处理搜索
    onSearch() {
      this.fetchTaskList();
    },

    // Navigation
    goToSearch() {
      uni.navigateTo({
        url: '/pages/myTask/search'
      });
    },

    getMattersTypeText(types) {
      if (!types) return '';

      // 处理单个类型
      if (!types.includes(',')) {
        const category = this.categories?.find(cat => cat.dictValue == types);
        return category ? category.dictLabel : types;
      }

      // 处理多个类型
      const typeArray = types.split(',');
      const typeTexts = typeArray.map(type => {
        const found = this.categories?.find(cat => cat.dictValue == type);
        return found ? found.dictLabel : type;
      });

      return typeTexts.join(' | ');
    },

    sortTasks(tasks, order) {
      return [...tasks].sort((a, b) => {
        const dateA = a.planTime || a.date;
        const dateB = b.planTime || b.date;

        if (order === 'asc') {
          return new Date(dateA) - new Date(dateB);
        } else {
          return new Date(dateB) - new Date(dateA);
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.task-container {
  background-color: #D6E1F1;
  min-height: 100vh;
}

.filter-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  background-color: #D6E1F1;
  border-radius: 12rpx;
  padding: 16rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.filter-item {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFFFFF;
  border-radius: 32rpx;
  padding: 10rpx 30rpx;
  margin-right: 16rpx;
  font-size: 28rpx;
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #F5F5F5;
  border-radius: 30rpx;
  padding: 0 20rpx;
  margin-left: 10rpx;
  height: 70rpx;
}

/* .filter-item {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F5F7FA;
  border-radius: 32rpx;
  padding: 12rpx 30rpx;
  margin-right: 16rpx;
  font-size: 28rpx;
}
 */
.arrow-down {
  margin-left: 8rpx;
  font-size: 20rpx;
  color: #999;
}

/* .search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #F5F7FA;
  border-radius: 32rpx;
  padding: 12rpx 30rpx;
}
 */
.icon-search {
  margin-right: 10rpx;
  font-size: 24rpx;
  color: #999;
}

.search-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
  color: #333;
  background-color: transparent;
}

.search-text {
  color: #999;
  font-size: 28rpx;
}

.task-scroll-view {
  position: fixed;
  top: 100rpx;
  /* filter-area的高度 */
  left: 0;
  right: 0;
  bottom: 0;
}

.task-list {
  padding: 20rpx 24rpx 0 24rpx;
}

.task-card {
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.task-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.task-title {
  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
}

.task-date {
  font-size: 28rpx;
  color: #999;
}

.task-subheader {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.person-type {
  display: flex;
}

.task-person,
.task-type {
  font-size: 28rpx;
  color: #666;
  margin-right: 16rpx;
}

.task-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.task-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}

.action-btn {
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 8rpx;
  text-align: center;
}

.view-btn {
  background-color: #E8F3FF;
  color: #4080FF;
}

.feedback-btn {
  /*  background-color: #F6FFED;
  color: #52C41A; */
  background-color: #4080FF;
  color: white;
}

.loading-state,
.empty-state {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

.popup-content {
  background-color: #FFFFFF;
  border-radius: 16rpx 16rpx 0 0;
  padding: 30rpx;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 90rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.cancel-btn,
.confirm-btn {
  font-size: 28rpx;
  padding: 10rpx;
}

.cancel-btn {
  color: #666666;
}

.confirm-btn {
  color: #4080FF;
}

.popup-body {
  max-height: 60vh;
  padding: 20rpx 0;
  overflow-y: auto;
}

.checkbox-item {
  display: block;
  padding: 20rpx 30rpx;
}

.option-label {
  display: flex;
  align-items: center;
}

.option-text {
  font-size: 28rpx;
  color: #333333;
  margin-left: 10rpx;
}

/*.status-draft {
  color: #4080FF;
}

.status-in-progress {
  color: #E6A23C;
}

.status-on-time {
  color: #52C41A;
}

.status-delayed-finished {
  color: #FF7875;
}

.status-delayed-unfinished {
  color: #FFFFFF;
  background-color: #FF4D4F;
  padding: 0 10rpx;
  border-radius: 8rpx;
}

.status-ended {
  color: #999999;
}

.status-approving {
  color: #FAAD14;
}

.status-rejected {
  color: #FFFFFF;
  background-color: #FF4D4F;
  padding: 0 10rpx;
  border-radius: 8rpx;
}*/
.loading-more,
.no-more,
.empty-list {
  text-align: center;
  padding: 30rpx 0;
  color: #999999;
  font-size: 24rpx;
}
</style>
