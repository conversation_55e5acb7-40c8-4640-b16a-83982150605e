<template>
  <view :class="['create-container', { 'keyboard-active-matters': isContentFocused || isSolutionFocused }]">
    <!-- 基本信息卡片 -->
    <view class="card">
      <view class="form-item">
        <text class="form-label"><text class="required-mark">*</text>来源</text>
        <view class="form-right">
          <uni-icons type="personadd" :size="20" style="margin-right: 10rpx;color: #376DF7;"></uni-icons>
          <text class="form-value custom-arrow-right" @tap="openSourcePopup">{{formData.ranterName.join &&formData.ranterName.join(',') || '请选择'}}</text>
          <!-- <text class="arrow-icon">></text> -->
        </view>
       
      </view>
      
      <view class="form-item">
        <text class="form-label"><text class="required-mark">*</text>类型</text>
        <view class="form-right"  @tap="openTypePopup">
          <uni-icons type="plus" :size="20" style="margin-right: 10rpx;color: #376DF7;"></uni-icons>
          <text class="form-value custom-arrow-right">{{formData.mattersTypeNames && formData.mattersTypeNames.length > 0 ? formData.mattersTypeNames.join(',') : '请选择'}}</text>
          <!-- <text class="arrow-icon">></text> -->
        </view>
      </view>
      
      <view class="form-item" @tap="openCategoryPopup">
        <text class="form-label"><text class="required-mark">*</text>分类</text>
        <view class="form-right">
          <uni-icons type="plus" :size="20" style="margin-right: 10rpx;color: #376DF7;"></uni-icons>
          <text class="form-value custom-arrow-right" @tap="openCategoryPopup">{{formData.rantClassify || '请选择'}}</text>
          <!-- <text class="arrow-icon">></text> -->
        </view>
      </view>
    </view>
    
    <!-- 内容卡片 -->
    <view class="card">
      <view class="card-title"><text class="required-mark">*</text>内容</view>
      <view class="textarea-box">
        <textarea
          v-model="formData.rantContent"
          placeholder="请填写"
          maxlength="500"
          class="content-textarea"
        />
      </view>
    </view>
    
    <!-- 措施卡片 -->
    <view class="card">
      <view class="card-title"><text class="required-mark" v-if="!onlyTall">*</text>措施</view>
      <view class="textarea-box">
        <textarea
          v-model="formData.solution"
          placeholder="请填写"
          maxlength="500"
          class="content-textarea"
          @focus="handleSolutionFocus"
          @blur="handleSolutionBlur"
        />
      </view>
    </view>
    
    <!-- 负责信息卡片 -->
    <view class="card">
      <view class="form-item" @tap="openResponsiblePopup">
        <text class="form-label"><text class="required-mark">*</text>责任人</text>
        <view class="form-right">
          <uni-icons type="person" :size="20" style="margin-right: 10rpx;color: #376DF7;"></uni-icons>
          <text class="form-value custom-arrow-right" @tap="openResponsiblePopup">{{formData.responsiblePersonName || '请选择'}}</text>
          <!-- <text class="arrow-icon">></text> -->
        </view>
      </view>
      
      <view class="form-item" @tap="openDepartmentPopup">
        <text class="form-label"><text class="required-mark">*</text>责任部门</text>
        <view class="form-right">
          <uni-icons type="tune" :size="20" style="margin-right: 10rpx;color: #376DF7;"></uni-icons>
          <text class="form-value custom-arrow-right" @tap="openDepartmentPopup">{{formData.deptName || '请选择'}}</text>
          <!-- <text class="arrow-icon">></text> -->
        </view>
      </view>
      
      <view class="form-item" @tap="openDepartmentLeaderPopup">
        <text class="form-label">责任部门负责人</text>
        <view class="form-right">
          <!-- @tap="openDepartmentLeaderPopup" -->
          <text class="form-value">{{formData.respDeptResponsiblerName || '--'}}</text>
          <!-- <text class="arrow-icon">></text> -->
        </view>
      </view>
      
      <view class="form-item" @tap="openLeaderPopup" v-if="showRespDeptLeader">
        <text class="form-label">分管领导</text>
        <view class="form-right">
          <!-- @tap="openLeaderPopup" -->
          <text class="form-value">{{formData.respDeptLeaderName || '--'}}</text>
          <!-- <text class="arrow-icon">></text> -->
        </view>
      </view>
      
      <!-- @tap="openDatePopup" -->
      <view class="form-item">
        <text class="form-label"><text class="required-mark" v-if="!onlyTall">*</text>计划完成时间</text>
        <view class="form-right">
          <picker
          mode="date"
          :value="formData.planTime"
          @change="onDateChange"
        >
          <view class="form-value custom-arrow-right">
            <uni-icons type="list" :size="20" style="margin-right: 10rpx;color: #376DF7;"></uni-icons>
            {{formData.planTime || '选择日期'}}
          </view>
        </picker>
          <!-- <text class="form-value custom-arrow-right" @tap="openDatePopup">{{formData.planTime || '请选择'}}</text> -->
          <!-- <text class="arrow-icon">></text> -->
        </view>
      </view>
      
     <!--  <view class="form-item" @tap="openCreatorPopup">
        <text class="form-label">创建人</text>
        <view class="form-right">
          <text class="form-value">{{formData.createByName || '请选择'}}</text>
        </view>
      </view> -->
    </view>
    
    <!-- 私密设置卡片 -->
    <view class="card">
      <view class="form-item">
        <text class="form-label"><text class="required-mark">*</text>是否私密</text>
        <view class="radio-group">
          <view class="radio-item" @tap="setPrivate(true)">
            <view :class="['radio-circle', formData.isPrivate ? 'checked' : '']">
              <view v-if="formData.isPrivate" class="radio-inner"></view>
            </view>
            <text class="radio-text">是</text>
          </view>
          <view class="radio-item" @tap="setPrivate(false)">
            <view :class="['radio-circle', !formData.isPrivate ? 'checked' : '']">
              <view v-if="!formData.isPrivate" class="radio-inner"></view>
            </view>
            <text class="radio-text">否</text>
          </view>
        </view>
      </view>
      
      <view class="form-item" v-if="formData.isPrivate" @tap="openDeptVisiblePopup">
        <text class="form-label">可见部门</text>
        <view class="form-right min-width-300">
          <uni-icons type="bars" :size="20" style="margin-right: 10rpx;color: #376DF7;"></uni-icons>
          <text class="form-value">{{ formData.visibleDeptNames && formData.visibleDeptNames.length ? formData.visibleDeptNames.join(',') || '请选择' : '请选择' }}</text>
          <text class="arrow-icon">></text>
        </view>
      </view>
      
      <view class="form-item" v-if="formData.isPrivate" @tap="openUserVisiblePopup">
        <text class="form-label">可见人员</text>
        <view class="form-right min-width-300">
          <!-- <text class="form-value" v-if="visibleUserNickNames && visibleUserNickNames.length">{{ visibleUserNickNames && visibleUserNickNames.length ? visibleUserNickNames.join(',') : '请选择' }}</text> -->
          <uni-icons type="person" :size="20" style="margin-right: 10rpx;color: #376DF7;"></uni-icons>
          <text class="form-value">{{ formData.visibleUserNames && formData.visibleUserNames.length ? formData.visibleUserNames.join(',') || '请选择' : '请选择' }}</text>
          <text class="arrow-icon">></text>
        </view>
      </view>
      
      <view class="visible-tags" v-if="formData.isPrivate && ((formData.visibleDepts && formData.visibleDepts.length > 0) || (formData.visibleUsers && formData.visibleUsers.length > 0))">
        <view class="tag" v-for="(item, index) in formData.visibleDeptNames" :key="'dept-' + index">
          {{item}}
        </view>
        <view class="tag" v-for="(item, index) in visibleUserNames" :key="'user-' + index">
          {{item}}
        </view>
      </view>
    </view>
    
    <!-- 底部按钮 -->
    <view class="footer-btns" v-if="!useSubmitIn">
      <view class="btn btn-save" @tap="saveForm">保存</view>
      <view class="btn btn-submit" @tap="submitForm">提交</view>
    </view>
    <view class="footer-btns center" v-if="useSubmitIn">
      <view class="btn btn-submit" @tap="submitInForm">提交</view>
    </view>
     <!-- 选择来源,选择可见人员 -->
     <SelectUser ref="selectUser" :selectMultiple="true" @feedbackEmit="handleSelectUser" />
    <!-- 选择类型弹窗 -->
    <CategoryPickerNew ref="categoryPopupMattersType" :dictType="'rant_matters_type'" :multiple="true" @confirmWithDetail="selectType" />
    
    <!-- 选择分类弹窗 -->
   <CategoryPicker ref="categoryPopupClassify" :dictType="'rant_classify'" :hasAll="false" @confirmWithDetail="selectCategory" />

    <!-- 选择责任人 -->
    <SelectUser ref="selectResponsibleUser" @feedbackEmit="handleSelectResponsibleUser" />
    <!-- 选择责任部门 -->
    <DepartmentPicker ref="departmentPicker" @confirm="handleSelectDepartment" />
    <!-- 选择可见部门 -->
    <DeptVisible ref="deptVisible" :selectedDepts="formData.visibleDepts" @feedbackEmit="handleVisibleDeptSelect" />
  </view>
</template>

<script>
import { getMatters, addMatters, updateMatters, submitIn   } from '@/api/rant/matters.js';
import { getDicts } from '@/api/common.js';
import { listRespdet, getRespdet, deptTree } from '@/api/rant/respdet.js';
import SelectUser from '@/components/SelectUser/index.vue';
import DepartmentPicker from '@/components/DepartmentPicker/index.vue';
import CategoryPicker from '@/components/CategoryPicker/index.vue';
import CategoryPickerNew from '@/components/CategoryPickerNew/index.vue';
import DeptVisible from '@/components/DeptVisible/index.vue';

export default {
  components: {
    SelectUser,
    DepartmentPicker,
    CategoryPicker,
    CategoryPickerNew,
    DeptVisible
  },
  data() {
    return {
      formData: {
        id: '',
        mattersType: [],   // 类型（多选）
        rantClassify: '',  // 分类
        rantContent: '',   // 内容
        solution: '',      // 措施
        ranterName: '',    // 来源
        ranter: '',        // 来源ID
        deptId: '',        // 责任部门ID
        deptName: '',      // 责任部门名称
        responsiblePerson: '', // 责任人ID
        responsiblePersonName: '', // 责任人名称
        respDeptResponsibler: '', // 部门负责人ID
        respDeptResponsiblerName: '', // 部门负责人名称
        respDeptLeader: '',    // 分管领导ID
        respDeptLeaderName: '', // 分管领导名称
        planTime: '',      // 计划完成时间
        isPrivate: 0,      // 是否私密：0否，1是
        visibleDepts: [],  // 可见部门ID列表
        visibleDeptNames: [], // 可见部门名称列表
        visibleUsers: [],  // 可见人员ID列表
        visibleUserNames: [], // 可见人员名称列表
        visibleScope: '',  // 可见范围（显示文本）
        createBy: '',      // 创建人
        createByName: ''   // 创建人名称
      },
      isEdit: false,       // 是否编辑模式
      // 选项数据
      mattersTypeOptions: [],  // 类型选项
      classifyOptions: [],    // 分类选项
      id: null,               // 编辑ID
      // 部门和人员
      deptOptions: [],        // 部门选项
      userOptions: [],          // 人员选项
      userType: '',      // 选择类型：visible（可见人员），source（来源）
      useSubmitIn: false, // 是否使用传阅接口

      visibleUserNickNames: [], // 可见人员名称列表
      isContentFocused: false, // 控制内容textarea焦点状态
      isSolutionFocused: false, // 控制措施textarea焦点状态
      onlyTall: false, // 是否只有谈话反馈
    }
  },
  computed: {
    currentDate() {
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    showRespDeptLeader() { // 是否显示分管领导
      console.log('this.formData.mattersType', this.formData.mattersType, this.formData.mattersType.includes('3'));
      return this.formData.mattersType.includes('3');
    },
   /*  onlyTall() {
      debugger
      console.log('------', this.formData.mattersTypeNames && this.formData.mattersTypeNames.length > 0 && this.formData.mattersTypeNames[0] === '谈话反馈')
      return this.formData.mattersTypeNames && this.formData.mattersTypeNames.length > 0 && this.formData.mattersTypeNames[0] === '谈话反馈';
    } */
  },
  onLoad(options) {
    // 获取字典数据
    this.initDictData();
    
    if (options.id) {
      this.id = options.id;
      this.isEdit = options.type === 'edit';
      this.useSubmitIn = options.useSubmitIn === 'true';
      // 如果是编辑模式，修改页面标题
      if (this.isEdit) {
        uni.setNavigationBarTitle({
          title: '编辑督办事项'
        });
      }
      
      this.loadMatterDetail(options.id);
    } else {
      // 默认设置当前登录用户为创建人
      this.setCurrentUserAsCreator();
    }
  },
  methods: {
    openResponsiblePopup() {
      this.$refs.selectResponsibleUser.show();
    },
    handleSelectResponsibleUser(user) {
      this.formData.responsiblePerson = user.userId;
      this.formData.responsiblePersonName = user.nickName;
    },
    openSourcePopup() {
      this.userType = 'source'; // 选择来源
      this.$refs.selectUser.show();
    },
    // 初始化字典数据
    initDictData() {
      // 获取数据字典
      getDicts('rant_matters_type').then(res => {
        if (res.code === 200) {
          this.mattersTypeOptions = res.data;
           // 编辑模式下设置formData.mattersTypeNames，用于表单展示
           this.formData.mattersTypeNames = this.mattersTypeOptions.filter(item => this.formData.mattersType.includes(item.value)).map(item => item.label);
        }
      });
      
      getDicts('rant_classify').then(res => {
        if (res.code === 200) {
          this.classifyOptions = res.data;
        }
      });
      
      // 获取部门数据
      this.getDeptOptions();
      // 获取用户数据
      this.getUserOptions();
    },
    
    // 获取部门数据
    getDeptOptions() {
      listRespdet().then(res => {
        if (res.code === 200) {
          this.deptOptions = res.data;
        }
      }).catch(err => {
        console.error('获取部门数据失败:', err);
        uni.showToast({
          icon: 'none',
          title: '获取部门数据失败'
        });
      });
    },
    
    // 获取用户数据
    getUserOptions() {
      // 这里应根据PC端的实现调用获取用户数据的API
      // 例如 listUser 或类似API
      // 由于未提供具体API，这里仅提供示例实现
      // 实际开发中需要替换为真实API调用
     /*  uni.request({
        url: getApp().globalData.baseUrl + '/system/user/list',
        method: 'GET',
        header: {
          'Authorization': uni.getStorageSync('token')
        },
        success: (res) => {
          if (res.data && res.data.code === 200) {
            this.userOptions = res.data.rows || [];
          }
        },
        fail: (err) => {
          console.error('获取用户数据失败:', err);
        }
      }); */
    },
    
    // 加载事项详情
    loadMatterDetail(id) {
      uni.showLoading({
        title: '加载中...'
      });
      
      getMatters(id).then(res => {
        uni.hideLoading();
        
        if (res.code === 200) {
          const data = res.data;
          
          // 将后端数据映射到表单
          this.formData = {
            id: data.id,
            mattersType: data.mattersType ? data.mattersType.split(',') : [],
            rantClassify: data.rantClassify || '',
            rantContent: data.rantContent || '',
            solution: data.solution || '',
            ranterName: data.ranterName?.split(',') || [],
            ranter: data.ranter?.split(',') || [],
            deptId: data.deptId || '',
            deptName: data.deptName || '',
            responsiblePerson: data.responsiblePerson || '',
            responsiblePersonName: data.responsiblePersonName || '',
            respDeptResponsibler: data.respDeptResponsibler || '',
            respDeptResponsiblerName: data.respDeptResponsiblerName || '',
            respDeptLeader: data.respDeptLeader || '',
            respDeptLeaderName: data.respDeptLeaderName || '',
            planTime: data.planTime || '',
            isPrivate: data.isPrivate === 1,
            visibleDepts: data.visibleDepts ? (typeof data.visibleDepts === 'string' ? data.visibleDepts.split(',') : data.visibleDepts) : [],
            visibleDeptNames: data.visibleDeptNames || [],
            visibleUsers: data.visibleUsers ? (typeof data.visibleUsers === 'string' ? data.visibleUsers.split(',') : data.visibleUsers) : [],
            visibleUserNames: data.visibleUserNames || [],
            visibleScope: data.visibleScope || '',
            createBy: data.createBy || '',
            createByName: data.createByName || ''
          };
          
          // 编辑模式下设置formData.mattersTypeNames，用于表单展示
          this.formData.mattersTypeNames = this.mattersTypeOptions.filter(item => this.formData.mattersType.includes(item.dictValue)).map(item => item.dictLabel);

          getRespdet(data.deptId).then(response => {
            this.formData.deptName = response.data.name;
            this.formData.respDeptLeader = response.data.responsibleLeader;
            this.formData.respDeptLeaderName = response.data.responsibleLeaderName;
            this.formData.respDeptResponsibler = response.data.respPeople;
            this.formData.respDeptResponsiblerName = response.data.respPeopleName;
          });
          // 更新可见范围文本
          this.updateVisibleScope();
        } else {
          uni.showToast({
            icon: 'none',
            title: res.msg || '加载失败'
          });
        }
      }).catch(err => {
        uni.hideLoading();
        console.error('加载事项详情失败:', err);
        uni.showToast({
          icon: 'none',
          title: '网络错误，请稍后重试'
        });
      });
    },
    
    // 设置当前用户为创建人
    setCurrentUserAsCreator() {
      // 从缓存获取当前用户信息
      const userInfo = uni.getStorageSync('userInfo');
      if (userInfo) {
        this.formData.createBy = userInfo.userId;
        this.formData.createByName = userInfo.userName;
      }
    },
    
    // 选择类型
    selectType(types) {
      console.log('types-----------', types);
      this.formData.mattersType = types?.values;
      this.formData.mattersTypeNames = types?.labels;
      this.onlyTall = this.formData.mattersTypeNames 
      && this.formData.mattersTypeNames.length > 0 
      && this.formData.mattersTypeNames.length === 1 
      && this.formData.mattersTypeNames[0] === '谈话反馈';
    },
    
    // 选择分类
    selectCategory(category) {
      console.log('category-----------', category);
      this.formData.rantClassify = category?.labels[0];
      this.$refs.categoryPopupClassify.close();
    },
    
    // 选择责任人
    selectResponsible(user) {
      this.formData.responsiblePerson = user.userId;
      this.formData.responsiblePersonName = user.userName;
      this.$refs.responsiblePopup.close();
    },
    
    // 选择责任部门
    selectDepartment(dept) {
      this.formData.deptId = dept.deptId;
      this.formData.deptName = dept.deptName;
      // 自动获取部门负责人和分管领导
      this.getDeptLeaders(dept.deptId);
      this.$refs.departmentPicker.close();
    },
    
    // 获取部门负责人和分管领导
    getDeptLeaders(deptId) {
      // 根据PC端的实现，通过API获取部门负责人和分管领导
      getRespdet(deptId).then(res => {
        if (res.code === 200 && res.data) {
          const data = res.data;
          this.formData.respDeptResponsiblerName = data.respPeopleName || '';
          this.formData.respDeptResponsibler = data.respPeople || '';
          this.formData.respDeptLeaderName = data.responsibleLeaderName || '';
          this.formData.respDeptLeader = data.responsibleLeader || '';
        } else {
          // 如果接口返回无结果，清空相关字段
          this.formData.respDeptResponsiblerName = '';
          this.formData.respDeptResponsibler = '';
          this.formData.respDeptLeaderName = '';
          this.formData.respDeptLeader = '';
        }
      }).catch(err => {
        console.error('获取部门负责人和分管领导失败:', err);
        uni.showToast({
          icon: 'none',
          title: '获取部门负责人失败'
        });
      });
    },
    
    // 选择计划完成时间
    selectDate(date) {
      this.formData.planTime = date;
      this.$refs.datePopup.close();
    },
    
    // 设置是否私密
    setPrivate(isPrivate) {
      this.formData.isPrivate = isPrivate ? 1 : 0;
      
      // 如果设置为非私密，清空可见范围数据
      if (!isPrivate) {
        this.formData.visibleDepts = [];
        this.formData.visibleDeptNames = [];
        this.formData.visibleUsers = [];
        this.formData.visibleUserNames = [];
        this.formData.visibleScope = '';
      }
    },
    
    // 选择可见范围
    selectVisible(data) {
      if (data.depts) {
        this.formData.visibleDepts = data.depts;
      }
      if (data.users) {
        this.formData.visibleUsers = data.users;
      }
      // 更新可见范围文本
      this.updateVisibleScope();
      this.$refs.visiblePopup.close();
    },
    
    // 更新可见范围文本
    updateVisibleScope() {
      let scopeText = '';
      if (this.formData.visibleDeptNames && this.formData.visibleDeptNames.length) {
        scopeText += `部门(${this.formData.visibleDeptNames.length})`;
      }
      if (this.formData.visibleUserNames && this.formData.visibleUserNames.length) {
        if (scopeText) scopeText += ', ';
        scopeText += `人员(${this.formData.visibleUserNames.length})`;
      }
      this.formData.visibleScope = scopeText || '无';
    },
    
    // 表单验证
    validateForm() {
      if(!this.formData.ranter || this.formData.ranter.length === 0){
        uni.showToast({
          icon: 'none',
          title: '请选择来源'
        });
        return false;
      }
      if (!this.formData.mattersType || this.formData.mattersType.length === 0) {
        uni.showToast({
          icon: 'none',
          title: '请选择类型'
        });
        return false;
      }
      
      if (!this.formData.rantClassify) {
        uni.showToast({
          icon: 'none',
          title: '请选择分类'
        });
        return false;
      }
      
      if (!this.formData.rantContent) {
        uni.showToast({
          icon: 'none',
          title: '请填写内容'
        });
        return false;
      }
      
      if (!this.onlyTall && !this.formData.solution) {
        uni.showToast({
          icon: 'none',
          title: '请填写措施'
        });
        return false;
      }
      
      if (!this.formData.responsiblePerson) {
        uni.showToast({
          icon: 'none',
          title: '请选择责任人'
        });
        return false;
      }
      
      if (!this.formData.deptId) {
        uni.showToast({
          icon: 'none',
          title: '请选择责任部门'
        });
        return false;
      }
      
      if (!this.onlyTall && !this.formData.planTime) {
        uni.showToast({
          icon: 'none',
          title: '请选择计划完成时间'
        });
        return false;
      }
      
      // 如果是私密，必须选择可见范围
     /*  if (this.formData.isPrivate === 1 &&
          (!this.formData.visibleDepts || !this.formData.visibleDepts.length) &&
          (!this.formData.visibleUsers || !this.formData.visibleUsers.length)) {
        uni.showToast({
          icon: 'none',
          title: '请选择可见部门或人员'
        });
        return false;
      } */
      
      return true;
    },
    
    // 保存表单
    saveForm() {
      
      if (!this.validateForm()) return;
      
      // 处理表单数据，将数组转为逗号分隔的字符串
      const formData = this.processFormData();
      formData.status = 0; // 保存为草稿
      
      this.submitToServer(formData);
    },
    // 传阅提交
    submitInForm(){
      if (!this.validateForm()) return;
      uni.showLoading({
        title: '提交中...'
      });
      const formData = this.processFormData();
      
      // 确保 visibleDepts 和 visibleUsers 是字符串
      /* if (formData.isPrivate == 1) {
        if ((!formData.visibleDepts || formData.visibleDepts.length === 0) &&
            (!formData.visibleUsers || formData.visibleUsers.length === 0)) {
          uni.hideLoading();
          uni.showToast({
            icon: 'none',
            title: '请选择可见部门或人员'
          });
          return;
        }
        
        // 确保 visibleDepts 和 visibleUsers 是字符串
        if (Array.isArray(formData.visibleDepts)) {
          formData.visibleDepts = formData.visibleDepts.join(',');
        }
        
        if (Array.isArray(formData.visibleUsers)) {
          formData.visibleUsers = formData.visibleUsers.join(',');
        }
      } */
      
      const data = JSON.parse(JSON.stringify(formData));
      data.ranter = data.ranter?.join(',');
      data.ranterName = data.ranterName?.join(',');
      data.visibleDepts ? (typeof data.visibleDepts === 'string' ? data.visibleDepts.split(',') : data.visibleDepts) : []
      data.visibleDvisibleUsersepts ? (typeof data.visibleUsers === 'string' ? data.visibleUsers.split(',') : data.visibleUsers) : []
      // data.visibleDepts = data.visibleDepts?.split(',') || [] ;
      // data.visibleUsers = data.visibleUsers?.split(',') || [];
      const apiCall =  submitIn(data);
      
      apiCall.then(res => {
        uni.hideLoading();
        
        if (res.code === 200) {
          uni.showToast({
            icon: 'success',
            title: '操作成功'
          });
          
          // 返回列表页
          uni.navigateBack();
        } else {
          uni.showToast({
            icon: 'none',
            title: res.msg || '操作失败'
          });
        }
      }).catch(err => {
        uni.hideLoading();
      });
    },
    // 提交表单
    submitForm() {
      if (!this.validateForm()) return;
      
      // 处理表单数据，将数组转为逗号分隔的字符串
      const formData = this.processFormData();
      formData.status = 1; // 提交状态
      
      this.submitToServer(formData);
    },
    
    // 处理表单数据
    processFormData() {
      const formData = JSON.parse(JSON.stringify(this.formData));
      
      // 处理类型，将数组转为逗号分隔的字符串
      if (Array.isArray(formData.mattersType)) {
        formData.mattersType = formData.mattersType.join(',');
      }
      
      // 处理可见部门
     /*  if (Array.isArray(formData.visibleDepts)) {
        formData.visibleDepts = formData.visibleDepts.join(',');
      }
      
      // 处理可见人员
      if (Array.isArray(formData.visibleUsers)) {
        formData.visibleUsers = formData.visibleUsers.join(',');
      } */
      
      // 处理是否私密字段，确保为数字类型
      formData.isPrivate = formData.isPrivate ? 1 : 0;
      
      // 过滤掉空值参数
      return this.filterEmptyParams(formData);
    },
    
    // 过滤空参数
    filterEmptyParams(params) {
      const filteredParams = {};
      
      Object.keys(params).forEach(key => {
        const value = params[key];
        
        // 检查各种类型的空值
        if (value !== undefined && value !== null && value !== '') {
          // 对于字符串，去除空白字符后再检查
          if (typeof value === 'string') {
            const trimmed = value.trim();
            if (trimmed !== '') {
              filteredParams[key] = trimmed;
            }
          } else {
            filteredParams[key] = value;
          }
        }
      });
      
      return filteredParams;
    },
    
    // 提交到服务器
    submitToServer(formData) {
      uni.showLoading({
        title: '提交中...'
      });
      
      // 确保 visibleDepts 和 visibleUsers 是字符串
     /*  if (formData.isPrivate == 1) {
        if ((!formData.visibleDepts || formData.visibleDepts.length === 0) &&
            (!formData.visibleUsers || formData.visibleUsers.length === 0)) {
          uni.hideLoading();
          uni.showToast({
            icon: 'none',
            title: '请选择可见部门或人员'
          });
          return;
        }
        
        // 确保 visibleDepts 和 visibleUsers 是字符串
        if (Array.isArray(formData.visibleDepts)) {
          formData.visibleDepts = formData.visibleDepts.join(',');
        }
        
        if (Array.isArray(formData.visibleUsers)) {
          formData.visibleUsers = formData.visibleUsers.join(',');
        }
      } */
      
      const data = JSON.parse(JSON.stringify(formData));
      data.ranter = data.ranter.join(',');
      data.ranterName = data.ranterName.join(',');
      const apiCall = this.isEdit ? updateMatters(data) : addMatters(data);
      
      apiCall.then(res => {
        uni.hideLoading();
        
        if (res.code === 200) {
          uni.showToast({
            icon: 'success',
            title: '操作成功'
          });
          
          // 返回列表页
          uni.navigateBack();
        } else {
          uni.showToast({
            icon: 'none',
            title: res.msg || '操作失败'
          });
        }
      }).catch(err => {
        uni.hideLoading();
      });
    },
    
    // 打开类型弹窗
    openTypePopup() {
      // 如果是编辑模式且已有选中的类型，则预设选中值
      if (this.formData.mattersType && this.formData.mattersType.length) {
        this.$refs.categoryPopupMattersType.setSelectedValues(this.formData.mattersType);
      }
      this.$refs.categoryPopupMattersType.show();
    },
    
    // 关闭类型弹窗
    closeTypePopup() {
      this.$refs.categoryPopupMattersType.close();
    },
    
    // 打开分类弹窗
    openCategoryPopup() {
      this.$refs.categoryPopupClassify.show();
    },
    
    // 关闭分类弹窗
    closeCategoryPopup() {
      this.$refs.categoryPopupClassify.close();
    },
    
    
    // 关闭责任人弹窗
    closeResponsiblePopup() {
      this.$refs.responsiblePopup.close();
    },
    
    // 打开部门弹窗
    openDepartmentPopup() {
      this.$refs.departmentPicker.show();
    },
    
    // 关闭部门弹窗
    closeDepartmentPopup() {
      this.$refs.departmentPopup.close();
    },
    
    // 打开部门负责人弹窗
    openDepartmentLeaderPopup() {
      // this.$refs.departmentPicker.open();
    },
    
    // 关闭部门负责人弹窗
    closeDepartmentLeaderPopup() {
      this.$refs.departmentLeaderPopup.close();
    },
    
    // 打开分管领导弹窗
    openLeaderPopup() {
      this.$refs.leaderPopup.open();
    },
    
    // 关闭分管领导弹窗
    closeLeaderPopup() {
      this.$refs.leaderPopup.close();
    },
    
    // 打开可见范围弹窗
    openVisiblePopup() {
      if (this.deptVisibleOptions.length === 0) {
        this.getVisibleDeptOptions();
      }
      this.$refs.visiblePopup.open();
    },
    
    // 关闭可见范围弹窗
    closeVisiblePopup() {
      this.$refs.visiblePopup.close();
    },
    
    // 日期选择
    onDateChange(e) {
      this.formData.planTime = e.detail.value;
    },
    
    // 创建人选择
    openCreatorPopup() {
      if (this.userOptions.length === 0) {
        this.getUserOptions();
      }
      this.$refs.creatorPopup.open();
    },
    
    // 关闭创建人弹窗
    closeCreatorPopup() {
      this.$refs.creatorPopup.close();
    },
    
    // 选择创建人
    selectCreator(user) {
      this.formData.createBy = user.userId;
      this.formData.createByName = user.userName;
      this.closeCreatorPopup();
    },
    
    // 选择部门负责人
    selectDepartmentLeader(user) {
      this.formData.respDeptResponsibler = user.userId;
      this.formData.respDeptResponsiblerName = user.userName;
      this.closeDepartmentLeaderPopup();
    },
    
    // 选择分管领导
    selectLeader(user) {
      this.formData.respDeptLeader = user.userId;
      this.formData.respDeptLeaderName = user.userName;
      this.closeLeaderPopup();
    },
    
    // 切换可见部门
    toggleVisibleDept(dept) {
      const index = this.formData.visibleDepts.indexOf(dept.id);
      if (index === -1) {
        // 不存在，添加
        this.formData.visibleDepts.push(dept.id);
      } else {
        // 存在，移除
        this.formData.visibleDepts.splice(index, 1);
      }
    },
    
    // 确认可见范围
    confirmVisiblePopup() {
      this.updateVisibleScope();
      this.closeVisiblePopup();
    },
    
    // 切换部门展开状态
    toggleDeptExpand(item) {
      const id = item.id + '';
      const index = this.expandedDepts.indexOf(id);
      if (index === -1) {
        // 不存在，添加到展开列表
        this.expandedDepts.push(id);
      } else {
        // 存在，从展开列表移除
        this.expandedDepts.splice(index, 1);
      }
    },
    
    // 判断部门是否展开
    isExpanded(id) {
      return this.expandedDepts.includes(id + '');
    },
    
    // 打开可见部门选择弹窗
    openDeptVisiblePopup() {
      this.$refs.deptVisible.show();
    },
    
    // 打开可见人员选择弹窗
    openUserVisiblePopup() {
      this.userType = 'visible';  // 选择可见人员
      this.$refs.selectUser.show();
    },
    
    // 处理部门选择结果
    handleSelectDepartment(depts) {
      console.log('depts-----------', depts);
      this.formData.deptId = depts.id;
      this.formData.deptName = depts.name;
      // 获取部门负责人和分管领导
      this.getDeptLeaders(this.formData.deptId);
     /*  if (depts && Array.isArray(depts)) {
        // 区分单选和多选
        if (this.formData.isPrivate) {
          // 多选，用于可见部门
          this.formData.visibleDepts = depts.map(dept => dept.id || dept.deptId);
          this.formData.visibleDeptNames = depts.map(dept => dept.name || dept.deptName);
        } else {
          // 单选，用于责任部门
          this.formData.deptId = depts.id || depts.deptId;
          this.formData.deptName = depts.name || depts.deptName;
          // 获取部门负责人和分管领导
          this.getDeptLeaders(this.formData.deptId);
        }
      }
      this.updateVisibleScope(); */
    },
    
    // 处理用户选择结果
    handleSelectUser(users) {
      if (users && Array.isArray(users)) {
        if(this.userType === 'visible'){
          // 用于可见人员
          this.formData.visibleUsers = users.map(user => user.userId);
          // this.formData.visibleUserNames = users.map(user => user.userName);
          this.formData.visibleUserNames = users.map(user => user.nickName);
          // this.visibleUserNickNames = users.map(user => user.nickName);
        } else if (this.userType === 'source') {
          // 用于来源
          this.formData.ranterName = users.map(user => user.nickName);
          this.formData.ranter = users.map(user => user.userId);

          console.log('this.formData.ranterName-----------', this.formData.ranterName);
          console.log('this.formData.ranter-----------', this.formData.ranter);

          console.log('users-----------', users);
        }
      } /* else if (users) {
        // 单个用户
        this.formData.visibleUsers = [users.userId];
        this.formData.visibleUserNames = [users.userName];
      }
      this.updateVisibleScope(); */
    },
    
    // 获取有子部门的部门列表
    getChildrenWithSubDepts(depts) {
      if (!depts || !Array.isArray(depts)) return [];
      return depts.filter(dept => dept.children && dept.children.length > 0);
    },
    
    // 处理可见部门选择
    handleVisibleDeptSelect(selectedDepts) {
      this.formData.visibleDepts = selectedDepts.deptIds;
      this.formData.visibleDeptNames = selectedDepts.deptNames;
      this.updateVisibleScope();
    },

    handleContentFocus() {
      this.isContentFocused = true;
      // 延迟滚动，等待DOM更新完成
      this.$nextTick(() => {
        setTimeout(() => {
          // 使用兼容性滚动工具滚动到页面底部
          this.$scrollHelper.scrollToBottom({
            duration: 300,
            success: () => {
              console.log('滚动到底部成功');
            },
            fail: (error) => {
              console.warn('滚动到底部失败:', error);
            }
          });
        }, 0);
      });
    },

    handleContentBlur() {
      this.isContentFocused = false;
      // 可选：恢复到合适的滚动位置
      setTimeout(() => {
        this.$scrollHelper.scrollToTop({
          duration: 300
        });
      }, 100);
    },

    handleSolutionFocus() {
      this.isSolutionFocused = true;
      // 延迟滚动，等待DOM更新完成
      this.$nextTick(() => {
        setTimeout(() => {
          // 使用兼容性滚动工具滚动到页面底部
          this.$scrollHelper.scrollToBottom({
            duration: 300,
            success: () => {
              console.log('滚动到底部成功');
            },
            fail: (error) => {
              console.warn('滚动到底部失败:', error);
            }
          });
        }, 0);
      });
    },

    handleSolutionBlur() {
      this.isSolutionFocused = false;
      // 可选：恢复到合适的滚动位置
      setTimeout(() => {
        this.$scrollHelper.scrollToTop({
          duration: 300
        });
      }, 100);
    },
  }
};
</script>

<style scoped lang="scss">
.min-width-300 {
  min-width: 300rpx;
  justify-content: flex-end;
}

.min-width-150 {
  min-width: 150rpx;
}


.create-container {
  background-color: #D6E1F1;
  padding: 24rpx 24rpx 132rpx;
  min-height: 100vh;
}

.create-container.keyboard-active-matters {
  padding-bottom: 30vh;
}

.card {
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.card-title {
  font-weight: 500;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 88rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  word-break: keep-all;
  font-size: 28rpx;
  color: #333333;
  margin-right: 16rpx;
}

.form-right {
  display: flex;
  align-items: center;
}

.form-value {
  font-size: 28rpx;
  color: #999999;
  text-align: right;
}

.arrow-icon {
  margin-left: 8rpx;
  color: #CCCCCC;
  font-size: 24rpx;
}

.textarea-box {
  background-color: #F5F5F5;
  border-radius: 8rpx;
  padding: 16rpx;
  min-height: 200rpx;
}

.content-textarea {
  width: 100%;
  height: 200rpx;
  font-size: 28rpx;
}

.radio-group {
  display: flex;
}

.radio-item {
  display: flex;
  align-items: center;
  margin-right: 40rpx;
}

.radio-circle {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #CCCCCC;
  display: flex;
  justify-content: center;
  align-items: center;
}

.radio-circle.checked {
  border-color: #4080FF;
}

.radio-inner {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #4080FF;
}

.radio-text {
  margin-left: 8rpx;
  font-size: 28rpx;
  color: #333333;
}

.visible-tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: 16rpx;
}

.tag {
  background-color: #F0F7FF;
  color: #4080FF;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  border-radius: 4rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

.footer-btns {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background-color: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  &.center{
    display: flex;
    justify-content: center;
  }
}

.btn {
  width: 48%;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  text-align: center;
  font-size: 32rpx;
}

.btn-save {
  background-color: #F0F2F5;
  color: #333333;
}

.btn-submit {
  background-color: #4080FF;
  color: #FFFFFF;
}

/* 弹出层样式 */
.popup-content {
  background-color: #FFFFFF;
  border-radius: 16rpx 16rpx 0 0;
  padding: 30rpx;
}

.popup-title {
  font-weight: 500;
  font-size: 30rpx;
  color: #333333;
  text-align: center;
  margin-bottom: 30rpx;
}

.popup-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.popup-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
  font-size: 28rpx;
  color: #333333;
}

.popup-item .selected {
  color: #4080FF;
  font-weight: 500;
}

.popup-footer {
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
}

.popup-btn {
  width: 90%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #F5F5F5;
  color: #333333;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.popup-subtitle {
  font-size: 28rpx;
  color: #666666;
  margin: 20rpx 0 10rpx;
}

.popup-btn-confirm {
  background-color: #4080FF;
  color: #FFFFFF;
  margin-bottom: 20rpx;
}

.date-picker {
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  color: #4080FF;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  margin: 20rpx 0;
}

.checkbox-item {
  display: block;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.checkbox-label {
  display: flex;
  align-items: center;
}

.option-text {
  font-size: 28rpx;
  color: #333333;
  margin-left: 10rpx;
}

.dept-tree-item {
  display: flex;
  flex-wrap: wrap;
}

.dept-item {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.dept-children {
  margin-left: 20rpx;
}

.dept-child-item {
  margin-left: 20rpx;
}

.dept-has-more {
  margin-left: 20rpx;
}

.more-text {
  font-size: 24rpx;
  color: #999999;
}

.side-popup-content {
  background-color: #FFFFFF;
  border-radius: 16rpx 0 0 16rpx;
  padding: 30rpx;
}

.side-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.side-popup-title {
  font-weight: 500;
  font-size: 30rpx;
  color: #333333;
}

.side-popup-close {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.close-icon {
  font-size: 28rpx;
  color: #CCCCCC;
}

.side-popup-body {
  max-height: 100%;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  flex-wrap: wrap;
}

.tree-node-content {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
}

.expand-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
  color: #CCCCCC;
}

.empty-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.dept-name {
  font-size: 28rpx;
  color: #333333;
}

.tree-children {
  margin-left: 20rpx;
}

.tree-node.child-node {
  margin-left: 20rpx;
}

.tree-node.sub-child-node {
  margin-left: 40rpx;
}

.side-popup-footer {
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
}

.footer-btn {
  width: 90%;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  text-align: center;
  font-size: 28rpx;
}

.cancel-btn {
  /* background-color: #F5F5F5; */
  color: #333333;
}

.confirm-btn {
  /* background-color: #4080FF; */
  color: #FFFFFF;
}
.min-width-100 {
  min-width: 100rpx;
  justify-content: flex-end;
}
</style>
