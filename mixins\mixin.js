// mixins/MyMixin.js
import { getDicts } from '@/api/common'
import {rantStatusOption} from '@/constant/index'
export default {

  data() {
    return {
     types: [],
    };
  },
  created() {

  },
  onLoad (option) {
    this.fetchTypes();
  },
  methods: {
    // 设置导航栏标题
    setNavTitle(title) {
      uni.setNavigationBarTitle({
        title: title
      });
    },
    getStatusText(status) {
      const statusValue = Number(status);
      const statusObj = rantStatusOption?.find(s => s.value === statusValue);
      return statusObj ? statusObj.label : status;
    },
     // 获取类型选项
     async fetchTypes() {
      try {
        // 使用getDicts API获取字典数据
        const res = await getDicts('rant_matters_type');
        if (res.code === 200 && res.data) {
          // 添加"全部"选项
          this.types = [{ dictValue: '', dictLabel: '全部' }, ...res.data.map(item => ({
            dictValue:item.dictValue,
            dictLabel: item.dictLabel
          }))];
        }
      } catch (error) {
        console.error('获取类型选项失败:', error);
        uni.showToast({
          icon: 'none',
          title: '获取类型选项失败'
        });
      }
    },
    getTaskTypeLabel(value) {
      if (!value) return '';  
      
      // If value is a comma-separated string (from API), split it into an array
      const typeValues = typeof value === 'string' ? value.split(',') : 
                         Array.isArray(value) ? value : [value];
      
      // Map each value to its label
      const typeLabels = typeValues.map(val => {
        const type = this.types?.find(t => t.dictValue === val);
        return type ? type.dictLabel : '';
      }).filter(label => label); // Remove empty labels
      
      return typeLabels.join('|');
    },
    getStatusClass(status) {
      const statusValue = Number(status);
      const statusObj = rantStatusOption?.find(s => s.value === statusValue);
      
      if (!statusObj) return '';
      
      switch(statusValue) {
        case 0: // 草稿
          return 'status-draft';
        case 1: // 进行中
          return 'status-in-progress';
        case 2: // 按时完成
          return 'status-on-time';
        case 3: // 延期完成
          return 'status-delayed-finished';
        case 4: // 延期未完成
          return 'status-delayed-unfinished';
        case 5: // 终止
          return 'status-ended';
        case 6: // 审批中
          return 'status-approving';
        case 7: // 驳回
          return 'status-rejected';
        default:
          return '';
      }
    },
  }
};
