<template>
  <Layout>
    <view class="download-test">
      <view class="header">
        <text class="title">文件下载功能测试</text>
      </view>
      
      <view class="test-section">
        <view class="section-title">基础功能测试</view>
        
        <view class="test-item">
          <text class="test-label">1. 下载PDF文档</text>
          <button @click="testDownloadPDF" class="test-btn">测试下载</button>
        </view>
        
        <view class="test-item">
          <text class="test-label">2. 预览图片</text>
          <button @click="testPreviewImage" class="test-btn">测试预览</button>
        </view>
        
        <view class="test-item">
          <text class="test-label">3. 保存图片到相册</text>
          <button @click="testSaveImage" class="test-btn">测试保存</button>
        </view>
        
        <view class="test-item">
          <text class="test-label">4. 智能预览</text>
          <button @click="testQuickPreview" class="test-btn">测试智能预览</button>
        </view>
        
        <view class="test-item">
          <text class="test-label">5. 批量下载</text>
          <button @click="testBatchDownload" class="test-btn">测试批量下载</button>
        </view>
      </view>
      
      <view class="test-section">
        <view class="section-title">全局方法测试</view>
        
        <view class="test-item">
          <text class="test-label">使用 this.$downloadFile</text>
          <button @click="testGlobalDownload" class="test-btn">测试全局方法</button>
        </view>
        
        <view class="test-item">
          <text class="test-label">使用 this.$previewFile</text>
          <button @click="testGlobalPreview" class="test-btn">测试全局预览</button>
        </view>
      </view>
      
      <view class="test-section">
        <view class="section-title">测试结果</view>
        <view class="result-area">
          <text class="result-text">{{ testResult }}</text>
        </view>
      </view>
    </view>
  </Layout>
</template>

<script>
// 导入下载工具方法
import { 
  downloadFile, 
  previewFile, 
  saveImageToAlbum, 
  downloadFiles, 
  quickPreview 
} from '@/utils/downloadHelper'

export default {
  name: 'DownloadTest',
  data() {
    return {
      testResult: '等待测试...',
      // 测试用的文件URL（请替换为实际可用的URL）
      testFiles: {
        pdf: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        image: 'https://picsum.photos/800/600',
        doc: 'https://file-examples.com/storage/fe68c8c7c66d7b62c8e8b3e/2017/10/file_example_DOC_100kB.doc'
      }
    }
  },
  methods: {
    // 测试下载PDF
    async testDownloadPDF() {
      try {
        this.testResult = '正在下载PDF...'
        const result = await downloadFile(this.testFiles.pdf, {
          fileName: '测试文档.pdf',
          autoOpen: true
        })
        this.testResult = `PDF下载成功: ${JSON.stringify(result, null, 2)}`
      } catch (error) {
        this.testResult = `PDF下载失败: ${error.message}`
        console.error('PDF下载失败:', error)
      }
    },
    
    // 测试预览图片
    async testPreviewImage() {
      try {
        this.testResult = '正在预览图片...'
        const result = await previewFile(this.testFiles.image)
        this.testResult = `图片预览成功: ${JSON.stringify(result, null, 2)}`
      } catch (error) {
        this.testResult = `图片预览失败: ${error.message}`
        console.error('图片预览失败:', error)
      }
    },
    
    // 测试保存图片
    async testSaveImage() {
      try {
        this.testResult = '正在保存图片...'
        const result = await saveImageToAlbum(this.testFiles.image)
        this.testResult = `图片保存成功: ${JSON.stringify(result, null, 2)}`
      } catch (error) {
        this.testResult = `图片保存失败: ${error.message}`
        console.error('图片保存失败:', error)
      }
    },
    
    // 测试智能预览
    async testQuickPreview() {
      try {
        this.testResult = '正在智能预览...'
        const result = await quickPreview(this.testFiles.pdf)
        this.testResult = `智能预览成功: ${JSON.stringify(result, null, 2)}`
      } catch (error) {
        this.testResult = `智能预览失败: ${error.message}`
        console.error('智能预览失败:', error)
      }
    },
    
    // 测试批量下载
    async testBatchDownload() {
      try {
        this.testResult = '正在批量下载...'
        const urls = [
          this.testFiles.image,
          this.testFiles.pdf
        ]
        const result = await downloadFiles(urls, {
          concurrent: 2,
          showProgress: true
        })
        this.testResult = `批量下载完成: 成功${result.successCount}个，失败${result.errorCount}个`
      } catch (error) {
        this.testResult = `批量下载失败: ${error.message}`
        console.error('批量下载失败:', error)
      }
    },
    
    // 测试全局下载方法
    async testGlobalDownload() {
      try {
        this.testResult = '正在使用全局方法下载...'
        const result = await this.$downloadFile(this.testFiles.pdf, {
          fileName: '全局方法测试.pdf'
        })
        this.testResult = `全局下载成功: ${JSON.stringify(result, null, 2)}`
      } catch (error) {
        this.testResult = `全局下载失败: ${error.message}`
        console.error('全局下载失败:', error)
      }
    },
    
    // 测试全局预览方法
    async testGlobalPreview() {
      try {
        this.testResult = '正在使用全局方法预览...'
        const result = await this.$previewFile(this.testFiles.image)
        this.testResult = `全局预览成功: ${JSON.stringify(result, null, 2)}`
      } catch (error) {
        this.testResult = `全局预览失败: ${error.message}`
        console.error('全局预览失败:', error)
      }
    }
  },
  
  onLoad() {
    this.testResult = '页面加载完成，可以开始测试'
  }
}
</script>

<style scoped>
.download-test {
  padding: 20rpx;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  margin-bottom: 40rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #2c3e50;
}

.test-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.test-item:last-child {
  border-bottom: none;
}

.test-label {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.test-btn {
  padding: 15rpx 30rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 26rpx;
}

.test-btn:active {
  background: #0056cc;
}

.result-area {
  min-height: 200rpx;
  padding: 20rpx;
  background: white;
  border-radius: 8rpx;
  border: 1rpx solid #ddd;
}

.result-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  word-break: break-all;
}
</style>
