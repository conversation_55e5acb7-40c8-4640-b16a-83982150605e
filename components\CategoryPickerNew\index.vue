<template>
  <view class="category-picker">
    <uni-popup ref="popup" type="bottom">
      <view class="picker-content">
        <view class="picker-header">
          <text class="cancel-btn" @tap="handleCancel">取消</text>
          <text class="title">{{title}}</text>
          <text class="confirm-btn" @tap="handleConfirm">确定</text>
        </view>
        <view class="picker-body">
          <template v-if="multiple">
            <checkbox-group @change="handleChange">
              <label v-for="(item, index) in options" :key="index" class="checkbox-item">
                <view class="option-label">
                  <checkbox :value="item.dictValue" :checked="isSelected(item.dictValue)" color="#4080FF" />
                  <text class="option-text">{{item.dictLabel || item.name}}</text>
                </view>
              </label>
            </checkbox-group>
          </template>
          <template v-else>
            <radio-group @change="handleChange">
              <label v-for="(item, index) in options" :key="index" class="radio-item">
                <view class="option-label">
                  <radio :value="item.dictValue" :checked="isSelected(item.dictValue)" color="#4080FF" />
                  <text class="option-text">{{item.dictLabel || item.name}}</text>
                </view>
              </label>
            </radio-group>
          </template>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { getDicts } from '@/api/common'

export default {
  name: 'CategoryPicker',
  props: {
    // 标题
    title: {
      type: String,
      default: '请选择'
    },
    // 字典类型，使用getDicts API获取
    dictType: {
      type: String,
      default: ''
    },
    // 自定义选项列表，格式：[{value: '1', name: '选项1'}, {value: '2', name: '选项2'}]
    customOptions: {
      type: Array,
      default: () => []
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 是否显示"全部"选项
    hasAll: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      options: [],
      selectedValues: [], // 多选模式下的选中值数组
      selectedItems: [] // 多选模式下的选中项数组
    }
  },
  created() {
    if (this.dictType) {
      this.loadDictData()
    }
  },
  methods: {
    close() {
      this.$refs.popup.close()
    },
    // 加载字典数据
    async loadDictData() {
      try {
        const res = await getDicts(this.dictType)
        if (res.code === 200) {
          // 直接使用API返回的数据，不做映射转换
          if (this.hasAll) {
            this.options = [{dictValue: '', dictLabel: '全部'}, ...res.data]
          } else {
            this.options = res.data
          }
        } else {
          uni.showToast({
            title: '获取数据失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('获取字典数据失败:', error)
        uni.showToast({
          title: '获取数据失败',
          icon: 'none'
        })
      }
    },
    
    // 设置自定义选项
    setOptions(options) {
      this.options = options
    },
    
    // 显示弹窗
    show() {
      // 如果有自定义选项且未设置字典类型，使用自定义选项
      if (this.customOptions.length > 0 && !this.dictType) {
        this.options = this.customOptions
      } else if (this.dictType && this.options.length === 0) {
        // 如果设置了字典类型但还没加载数据，重新加载
        this.loadDictData()
      }
      
      this.$refs.popup.open('bottom')
    },
    
    // 检查值是否被选中
    isSelected(value) {
      return this.selectedValues.includes(value)
    },
    
    // 预选择值(支持多选)
    setSelectedValues(values) {
      if (Array.isArray(values)) {
        this.selectedValues = values
        this.updateSelectedItems()
      } else {
        this.selectedValues = [values].filter(Boolean)
        this.updateSelectedItems()
      }
    },
    
    // 更新选中项数组
    updateSelectedItems() {
      this.selectedItems = this.options.filter(item =>
        this.selectedValues.includes(item.dictValue)
      )
    },
    
    // 处理选择变更
    handleChange(e) {
      console.log('e-----------', e.detail.value);
      if (this.multiple) {
        const allValue = this.options.length > 0 && this.options[0].dictValue === '' ? this.options[0].dictValue : '';
        // 获取新的选中值
        let newSelectedValues = [...e.detail.value]; // 创建副本以避免修改原数组
        // 获取除"全部"外的所有项
        const allItems = this.options.filter(item => (item.dictValue) !== allValue);
        const allItemValues = allItems.map(item => item.dictValue );
        
        // 检查点击的是否是"全部"选项
        const wasAllSelected = this.selectedValues.includes(allValue);
        const isAllSelected = newSelectedValues.includes(allValue);
        
        // 检查是否是"全部"选项状态变化
        if (wasAllSelected !== isAllSelected) {
          // 如果选中了"全部"
          if (isAllSelected) {
            // 选中所有子项
            newSelectedValues = [allValue, ...allItemValues];
          } else {
            // 取消选中所有子项
            newSelectedValues = [];
          }
        } else {
          // 检查除"全部"外的所有子项是否都被选中
          const allOthersSelected = allItemValues.every(value => newSelectedValues.includes(value));
          console.log('allOthersSelected-----------', allOthersSelected);
          if (allOthersSelected) {
            // 如果所有子项都被选中，"全部"也应被选中
            if (!newSelectedValues.includes(allValue)) {
              newSelectedValues.push(allValue);
              console.log('newSelectedValues-----------', newSelectedValues);
            }
          } else {
            // 如果不是所有子项都被选中，"全部"不应被选中
            newSelectedValues = newSelectedValues.filter(value => value !== allValue);
            
            // 检查是否所有子项都未选中，如果是，则清空选择
            if (newSelectedValues.length === 0) {
              // 确保清空选择
              this.selectedValues = [];
              this.updateSelectedItems();
              return;
            }
          }
        }
        
        this.selectedValues = newSelectedValues;
      } else {
        this.selectedValues = [e.detail.value];
      }
      this.$forceUpdate();
      this.updateSelectedItems();
    },
    
    // 处理确认
    handleConfirm() {
      console.log('selectedValues---', this.selectedValues);
      // 处理返回数据
      let returnValues = [];
      const allValue = this.options.length > 0 && this.options[0].dictValue === '' ? this.options[0].dictValue : '';
      
      // 多选模式下的处理逻辑
      if (this.multiple) {
        // 检查是否选中了"全部"选项
        if (this.selectedValues.includes(allValue)) {
          // 获取除"全部"外的所有选项值作为返回值
          returnValues = this.options
            .filter(item => (item.dictValue) !== allValue)
            .map(item => item.dictValue);
        } else {
          // 没有选中"全部"，返回实际选中的值
          returnValues = this.selectedValues;
        }
      } else {
        // 单选模式
        returnValues = this.selectedValues[0] === allValue ? '' : this.selectedValues[0];
      }
      
      // 返回选中的值和标签
      const selectedLabels = [];
      const selectedResultItems = [];
      
      // 如果选择了"全部"，获取所有非"全部"选项的标签和项
      if (this.selectedValues.includes(allValue)) {
        this.options.forEach(item => {
          if ((item.dictValue) !== allValue) {
            selectedLabels.push(item.dictLabel || item.name);
            selectedResultItems.push(item);
          }
        });
      } else {
        // 否则只获取选中项的标签和项
        this.selectedItems.forEach(item => {
          if ((item.dictValue) !== allValue) {
            selectedLabels.push(item.dictLabel || item.name);
            selectedResultItems.push(item);
          }
        });
      }
      
      // 发送结果给父组件
      this.$emit('confirm', this.multiple ? returnValues : returnValues);
      this.$emit('confirmWithDetail', {
        values: returnValues,
        labels: selectedLabels,
        items: selectedResultItems
      });
      
      this.$refs.popup.close();
    },
    
    // 处理取消
    handleCancel() {
      this.$refs.popup.close()
    }
  }
}
</script>

<style lang="scss" scoped>
.category-picker {
  width: 100%;
}

.picker-content {
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 90rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #EEEEEE;
}

.title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.cancel-btn, .confirm-btn {
  font-size: 28rpx;
  padding: 10rpx;
}

.cancel-btn {
  color: #666666;
  border-radius: 10rpx;
}

.confirm-btn {
  color: #4080FF;
  border-radius: 10rpx;
}

.picker-body {
  max-height: 60vh;
  padding: 20rpx 0;
  overflow-y: auto;
}

.checkbox-item, .radio-item {
  display: block;
  padding: 20rpx 30rpx;
}

.option-label {
  display: flex;
  align-items: center;
}

.option-text {
  font-size: 28rpx;
  color: #333333;
  margin-left: 10rpx;
}
</style>
