/**
 * 文件下载工具 - 微信小程序和企业微信专用
 * 基于 fileHelper 封装，提供更简洁的下载API
 * 版本: v1.0.0
 * 
 * 使用方法:
 * 1. 在页面中导入: import { downloadFile, previewFile, downloadFiles } from '@/utils/downloadHelper'
 * 2. 或者使用全局方法: this.$downloadFile(url, options)
 */

import fileHelper from './fileHelper'

/**
 * 下载单个文件
 * @param {String} url 文件下载地址
 * @param {Object} options 配置选项
 * @returns {Promise} 下载结果
 */
export async function downloadFile(url, options = {}) {
  if (!url) {
    throw new Error('下载地址不能为空')
  }
  
  return await fileHelper.downloadFile(url, options)
}

/**
 * 预览文件（图片直接预览，文档下载后打开）
 * @param {String} url 文件地址
 * @param {Object} options 配置选项
 * @returns {Promise} 预览结果
 */
export async function previewFile(url, options = {}) {
  if (!url) {
    throw new Error('文件地址不能为空')
  }
  
  const defaultOptions = {
    autoOpen: true,
    showLoading: true,
    loadingText: '加载中...'
  }
  
  return await fileHelper.downloadFile(url, { ...defaultOptions, ...options })
}

/**
 * 下载图片到相册
 * @param {String} url 图片地址
 * @param {Object} options 配置选项
 * @returns {Promise} 下载结果
 */
export async function saveImageToAlbum(url, options = {}) {
  if (!url) {
    throw new Error('图片地址不能为空')
  }
  
  const defaultOptions = {
    saveToAlbum: true,
    autoOpen: false,
    showLoading: true,
    loadingText: '保存中...',
    showSuccessToast: true
  }
  
  return await fileHelper.downloadFile(url, { ...defaultOptions, ...options })
}

/**
 * 批量下载文件
 * @param {Array} urls 文件地址数组
 * @param {Object} options 配置选项
 * @returns {Promise} 下载结果
 */
export async function downloadFiles(urls, options = {}) {
  if (!urls || !Array.isArray(urls) || urls.length === 0) {
    throw new Error('文件地址数组不能为空')
  }
  
  return await fileHelper.downloadFiles(urls, options)
}

/**
 * 快速预览文件（简化版本）
 * 自动判断文件类型并选择最佳预览方式
 * @param {String} url 文件地址
 * @returns {Promise} 预览结果
 */
export async function quickPreview(url) {
  if (!url) {
    throw new Error('文件地址不能为空')
  }
  
  try {
    const fileInfo = fileHelper.getFileInfo(url)
    
    if (fileInfo.isImage) {
      // 图片直接预览
      return await previewFile(url, {
        loadingText: '加载图片中...'
      })
    } else {
      // 文档下载后打开
      return await previewFile(url, {
        loadingText: '下载文档中...'
      })
    }
  } catch (error) {
    console.error('快速预览失败:', error)
    throw error
  }
}

/**
 * 下载文件到本地（不自动打开）
 * @param {String} url 文件地址
 * @param {String} fileName 自定义文件名（可选）
 * @returns {Promise} 下载结果
 */
export async function downloadOnly(url, fileName = '') {
  if (!url) {
    throw new Error('文件地址不能为空')
  }
  
  return await downloadFile(url, {
    autoOpen: false,
    fileName: fileName,
    showSuccessToast: true,
    loadingText: '下载中...'
  })
}

/**
 * 静默下载（无提示）
 * @param {String} url 文件地址
 * @param {Object} options 配置选项
 * @returns {Promise} 下载结果
 */
export async function silentDownload(url, options = {}) {
  if (!url) {
    throw new Error('文件地址不能为空')
  }
  
  const defaultOptions = {
    showLoading: false,
    showSuccessToast: false,
    showErrorToast: false,
    autoOpen: false
  }
  
  return await downloadFile(url, { ...defaultOptions, ...options })
}

/**
 * 检查文件是否为图片
 * @param {String} url 文件地址
 * @returns {Boolean} 是否为图片
 */
export function isImageFile(url) {
  if (!url) return false
  return fileHelper.getFileInfo(url).isImage
}

/**
 * 检查文件是否为文档
 * @param {String} url 文件地址
 * @returns {Boolean} 是否为文档
 */
export function isDocumentFile(url) {
  if (!url) return false
  return fileHelper.getFileInfo(url).isDocument
}

/**
 * 获取文件信息
 * @param {String} url 文件地址
 * @returns {Object} 文件信息
 */
export function getFileInfo(url) {
  if (!url) return null
  return fileHelper.getFileInfo(url)
}

// 默认导出对象（包含所有方法）
export default {
  downloadFile,
  previewFile,
  saveImageToAlbum,
  downloadFiles,
  quickPreview,
  downloadOnly,
  silentDownload,
  isImageFile,
  isDocumentFile,
  getFileInfo
}

/**
 * 使用示例:
 * 
 * // 1. 预览文件（推荐）
 * import { previewFile } from '@/utils/downloadHelper'
 * await previewFile('https://example.com/file.pdf')
 * 
 * // 2. 下载文件
 * import { downloadFile } from '@/utils/downloadHelper'
 * await downloadFile('https://example.com/file.pdf', {
 *   autoOpen: false,
 *   fileName: '自定义文件名.pdf'
 * })
 * 
 * // 3. 保存图片到相册
 * import { saveImageToAlbum } from '@/utils/downloadHelper'
 * await saveImageToAlbum('https://example.com/image.jpg')
 * 
 * // 4. 批量下载
 * import { downloadFiles } from '@/utils/downloadHelper'
 * await downloadFiles([
 *   'https://example.com/file1.pdf',
 *   'https://example.com/file2.jpg'
 * ])
 * 
 * // 5. 快速预览（自动判断类型）
 * import { quickPreview } from '@/utils/downloadHelper'
 * await quickPreview('https://example.com/file.pdf')
 * 
 * // 6. 使用全局方法（在页面中）
 * this.$downloadFile('https://example.com/file.pdf')
 * this.$previewFile('https://example.com/image.jpg')
 */
