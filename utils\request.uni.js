import ConfigIndex from '@/config/index.config.js';
import store from '@/store';
import {handleLogin} from "@/utils/utils.js";

const isAbsoluteURL =  function (url) {
  return /^([a-z][a-z\d+\-.]*:)?\/\//i.test(url);
};

const combineURL = function (baseURL, relativeURL) {
  return relativeURL
    ? baseURL.replace(/\/+$/, "") + "/" + relativeURL.replace(/^\/+/, "")
    : baseURL
}

// 需要处理没有token和token过期逻辑
// 添加请求拦截器
uni.addInterceptor('request', {
  /*async invoke(args) {
    // 每个请求之前都会执行这个函数
    // 在这里添加全局的请求处理逻辑
    console.log('addInterceptor invoke-----------');
    if(!!uni.getStorageSync('access_token')){
      await handleLogin();
      return false
    }
    return args;
  },*/
  async success(response) {
    // 请求成功后的处理逻辑
    // console.log('addInterceptor success response------------', response);
    if(response.data.code === 401 || response.data.code === 402) { // 登录过期
      uni.setStorageSync('access_token', '');
      store.commit('setToken', '');
      // 打开登录
      uni.$emit('loginExpired');
    }
    else if(response.data.code === 500){ // 服务找不到
      // 清空token
      // uni.setStorageSync('access_token', '');
      // store.commit('setToken', '');
    }

    return response;
  },
  fail(err) {
    // 请求失败后的处理逻辑
    // console.log('addInterceptor fail response------------', err);
    return err;
  },
  complete(response) {
    // 请求完成后的处理逻辑
    // console.log('addInterceptor complete response------------', response);
  }
});
// const token = uni.getStorageSync('access_token'); // token可以存储在store中

function request (url, method, params, config = {}, needLogin) {
  const token = store.state.access_token;
  if(needLogin && !token) {
    return Promise.reject('need login');
  }
  config = {
    ...config
  }
  let headers = {}

  headers['Content-Type'] = 'application/json'; // 默认参数请求类型
  headers['Authorization'] = token ? `Bearer ${token}` : '';
  headers = {...headers, ...config.headers}

  return new Promise((resolve, reject) => {
    uni.request({
      url: ConfigIndex.baseUrl + url,
      method: method,
      data: params,
      header: headers,
      dataType:  'json',
      responseType: 'text',
      success: res => {
        resolve(res.data)
      },
      fail: (err) => {
        reject(err);
      },
      complete: (res) => {
      }
    })
  })
}

const requestCheckAuth =  (url, method, params, config, needLogin = true) => {
  if(needLogin && !store.state.access_token) {
    // 打开登录
    uni.$emit('loginExpired');
    return Promise.reject('token is empty');
  }
  return request(url, method, params, config, needLogin);
}


export function post (url, params, config = {}, needLogin = true) {
  return requestCheckAuth(url, 'POST', params, config, needLogin);
}

export function del (url, params, config = {}, needLogin = true) {
  return requestCheckAuth(url, 'DELETE', params, config, needLogin);
}

export function put (url, params, config = {}, needLogin = true) {
  return requestCheckAuth(url, 'PUT', params, config, needLogin);
}

export function get (url, params, config = {}, needLogin = true) {
  // 处理参数，将数组类型的参数格式化为逗号分隔的字符串
  if (params && typeof params === 'object') {
    const processedParams = { ...params };
    Object.keys(processedParams).forEach(key => {
      const value = processedParams[key];
      if (Array.isArray(value)) {
        processedParams[key] = value.join(',');
      }
    });
    return requestCheckAuth(url, 'GET', processedParams, config, needLogin);
  }
  return requestCheckAuth(url, 'GET', params, config, needLogin);
}
