import {getWxCode, mpWechatLogin} from "@/api/login";
import store from '@/store';
import { rantStatusOption } from '@/constant';
/**
 * 路由导航。
 * 参数：路径和查询参数。
 * 将查询参数附加到路径来构造要导航到的页面的URL
 */
 export function goPage(path, query) {
  if (query) {
    let queryStr = '';
    for (let key in query) {
      queryStr += `${key}=${query[key]}&`;
    }
    queryStr = queryStr.slice(0, -1);
    path += `?${queryStr}`;
  }
  uni.navigateTo({
    url: path
  });
}


/**
 * 用户登录过程。
 * 首先检查本地存储中是否存储了token。
 * 如果未找到token，它将尝试使用微信登录并存储新的token。
 */
export async function handleLogin(relaunch=true, showLoading=true) {
    try {
      if(showLoading){
        uni.showLoading({
          title: '登录中...'
        });
      }
      // type 1-企业微信 2-微信
      const data =  await getWxCode();
      const res = await mpWechatLogin(data);
      uni.hideLoading();
      if(res.code === 200){
       /*  if(!res.authFlag){
          wx.redirectTo({
            url: "/pages/index/error"
          });
          // goPage("/pages/index/error");
          return;
        }
        else{ */
          const newToken = res?.token?.access_token;
          if(!newToken){
            uni.showToast({
              title: '登录失败，请稍后重试',
              icon: 'none'
            });
            console.log('!newToken Failed to get new token', res);
            return;
          }
          const userInfo = res?.user;
          store.commit('setToken', newToken);
          store.commit('setUserInfo', userInfo);
          if (newToken) {
            uni.setStorageSync('access_token', newToken);
            uni.setStorageSync('userInfo', userInfo);
            if(relaunch){
              wx.reLaunch({
                url: '/pages/index/index'
              });
            }
            return new Promise(resolve => resolve(newToken));
          }
        // }

      } else {
        console.log('Failed to get new token', res);
        throw new Error('Failed to get new token');
      }
    } catch (e) {
      console.log('catch: Failed to get new token', e);
      uni.hideLoading();
      throw e; // re-throw the error to be caught in mounted
    }
}

export function emptyFilter(val) {
  if (parseFloat(val) !== 0 && (val === undefined || val === null) ) return '-'
  return val;
}

export function getLastTwelveWeeks() {
  const now = new Date(); // 获取当前日期和时间
  const dayOfWeek = now.getDay(); // 获取今天是周几 (0 = 周日, 1 = 周一, ..., 6 = 周六)
  const currentDay = now.getDate(); // 获取当前的日（几号）

  // 计算从今天往回推到本周的第一天（假设周一为一周的开始）
  const firstDayOfWeek = new Date(now);
  firstDayOfWeek.setDate(currentDay - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));

  // 计算12周前的日期
  const twelveWeeksAgo = new Date(firstDayOfWeek);
  twelveWeeksAgo.setDate(firstDayOfWeek.getDate() - 7 * 12); // 7天一周，乘以12周

  return {
    start: twelveWeeksAgo.toISOString().substring(0, 10), // 格式化日期为YYYY-MM-DD
    end: now.toISOString().substring(0, 10)
  };
}

export function getLastTwelveMonths() {
  const now = new Date(); // 获取当前日期
  const end = new Date(now.getFullYear(), now.getMonth() + 1, 0); // 当前月的最后一天
  const start = new Date(now.getFullYear(), now.getMonth() - 11, 1); // 12个月前的第一天

  return {
    start: start.toISOString().substring(0, 10), // 格式化日期为YYYY-MM-DD
    end: end.toISOString().substring(0, 10)
  };
}

export function customFilter(val){
  // 根据时间维度进行数字单位过滤，日：万元，其他：亿元
  // 目前接口中返回的数值都万元，所以这里处理金额不用直接除亿，除万获得实际亿数值
  if(val === undefined || val === null) return '-';
  if(val === 0) return 0;
  return (val / 10000).toFixed(2);
}

export function getStatusClass(status) {
  const statusValue = Number(status);
  const statusObj = rantStatusOption.find(s => s.value === statusValue);

  if (!statusObj) return '';

  switch(statusValue) {
    case 0: // 草稿
      return 'status-draft';
    case 1: // 进行中
      return 'status-in-progress';
    case 2: // 按时完成
      return 'status-on-time';
    case 3: // 延期完成
      return 'status-delayed-finished';
    case 4: // 延期未完成
      return 'status-delayed-unfinished';
    case 5: // 终止
      return 'status-ended';
    case 6: // 审批中
      return 'status-approving';
    case 7: // 驳回
      return 'status-rejected';
    default:
      return '';
  }
}
